{% extends "base.html" %}

{% block title %}设备管理{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题和统计卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <h2 class="mb-0"><i class="fas fa-microchip text-primary"></i> 设备管理</h2>

                <!-- 操作按钮组 -->
                <div class="d-flex gap-2 flex-wrap">
                    <!-- 主要操作按钮 -->
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
                            <i class="fas fa-plus me-1"></i>添加设备
                        </button>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#batchImportModal">
                            <i class="fas fa-file-import me-1"></i>批量导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作区域（彩色按钮版） -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-secondary">
            <div class="card-header bg-secondary bg-opacity-10">
                <h6 class="mb-0 text-secondary">
                    <i class="fas fa-tools me-2"></i>批量操作工具
                    <small class="text-muted ms-2">选择设备后一键执行</small>
                </h6>
            </div>
            <div class="card-body py-3">
                <!-- 第一行：设备选择操作 -->
                <div class="row g-2 mb-3">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-2 align-items-center">
                            <span class="badge bg-primary me-2">设备选择</span>

                            <button type="button" class="btn btn-primary btn-sm" onclick="selectAllDevices()">
                                <i class="fas fa-check-square me-1"></i>选中全部
                            </button>

                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="unselectAllDevices()">
                                <i class="fas fa-square me-1"></i>取消全部
                            </button>

                            <button type="button" class="btn btn-success btn-sm" onclick="selectOnlineDevices()">
                                <i class="fas fa-wifi me-1"></i>选中在线
                            </button>

                            <button type="button" class="btn btn-secondary btn-sm" onclick="selectOfflineDevices()">
                                <i class="fas fa-wifi-slash me-1"></i>选中离线
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 第二行：设备配置操作 -->
                <div class="row g-2 mb-3">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-2 align-items-center">
                            <span class="badge bg-danger me-2">设备配置</span>

                            <button type="button" class="btn btn-danger btn-sm batch-ota-btn">
                                <i class="fas fa-rocket me-1"></i>批量升级
                            </button>

                            <button type="button" class="btn btn-success btn-sm"
                                    onclick="showBatchSetParametersModal()">
                                <i class="fas fa-cogs text-white me-1"></i>批量设置参数
                            </button>

                            <button type="button" class="btn btn-warning btn-sm"
                                    onclick="showBatchServerConfigModal()">
                                <i class="fas fa-server text-dark me-1"></i>配置服务器
                            </button>

                            <button type="button" class="btn btn-info btn-sm"
                                    onclick="showBatchUpdateProductKeyModal()">
                                <i class="fas fa-key text-white me-1"></i>修改密钥
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 第三行：查询和调试操作 -->
                <div class="row g-2">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-2 align-items-center">
                            <span class="badge bg-purple me-2" style="background:#8b5cf6;">查询调试</span>

                            <button type="button" class="btn btn-indigo btn-sm"
                                    style="background:#6366f1;color:#fff;"
                                    onclick="showBatchQueryParametersModal()">
                                <i class="fas fa-clipboard-list me-1"></i>批量查询参数
                            </button>

                            <button type="button" class="btn btn-pink btn-sm"
                                    style="background:#ec4899;color:#fff;"
                                    onclick="showBatchQueryLocationsModal()">
                                <i class="fas fa-map-marked-alt me-1"></i>查询位置
                            </button>

                            <button type="button" class="btn btn-teal btn-sm"
                                    style="background:#14b8a6;color:#fff;"
                                    onclick="showBatchDebugScriptModal()">
                                <i class="fas fa-bug me-1"></i>调试脚本
                            </button>

                            <button type="button" class="btn btn-info btn-sm"
                                    onclick="showBatchQueryFirmwareInfoModal()">
                                <i class="fas fa-microchip me-1"></i>查询固件信息
                            </button>

                            <button type="button" class="btn btn-success btn-sm"
                                    onclick="batchRestartDebugScripts()"
                                    title="自动检测正在运行调试脚本的设备并重启">
                                <i class="fas fa-sync me-1"></i>重启运行中的调试脚本
                            </button>

                            <button type="button" class="btn btn-danger btn-sm"
                                    onclick="batchStopDebugScripts()"
                                    title="自动检测正在运行调试脚本的设备并停止">
                                <i class="fas fa-stop me-1"></i>停止运行中的调试脚本
                            </button>

                            <div class="ms-auto">
                                <span class="text-muted small">
                                    <i class="fas fa-check-circle me-1"></i>
                                    已选 <span id="selectedDevicesCount">0</span> 个设备
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- 统计卡片 -->
    <div class="row mb-4" id="statsCards">
        <div class="col-md-4">
            <div class="card border-primary h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-server fa-3x text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">设备总数</h6>
                            <h3 class="mb-0" id="totalDevices">{{ stats.total if stats else 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-success h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle fa-3x text-success"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">在线设备</h6>
                            <h3 class="mb-0" id="onlineDevices">{{ stats.online if stats else 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-warning h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">离线设备</h6>
                            <h3 class="mb-0" id="offlineDevices">{{ stats.offline if stats else 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备列表 -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0"><i class="fas fa-list text-primary"></i> 设备列表</h5>
                </div>
            </div>
            <!-- 添加筛选功能区域 -->
            <div class="row mt-3">
                <div class="col-12">
                    <!-- 基础筛选行 -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex gap-2 flex-wrap align-items-center">
                            <!-- 最常用的筛选：在线状态 -->
                            <div class="input-group" style="width: 160px;">
                                <span class="input-group-text"><i class="fas fa-wifi"></i></span>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">所有状态</option>
                                    <option value="online">在线设备</option>
                                    <option value="offline">离线设备</option>
                                </select>
                            </div>

                            <!-- 常用筛选：固件版本 -->
                            <div class="input-group" style="width: 160px;">
                                <span class="input-group-text"><i class="fas fa-code-branch"></i></span>
                                <input type="text" class="form-control" id="firmwareFilter" placeholder="固件版本...">
                            </div>

                            <!-- 常用筛选：升级状态 -->
                            <div class="input-group" style="width: 160px;">
                                <span class="input-group-text"><i class="fas fa-sync-alt"></i></span>
                                <select class="form-select" id="otaStatusFilter">
                                    <option value="all">升级状态</option>
                                    <option value="success">升级成功</option>
                                    <option value="failed">升级失败</option>
                                    <option value="none">未升级</option>
                                </select>
                            </div>

                            <!-- 产品密钥筛选 -->
                            <div class="input-group" style="width: 160px;">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input type="text" class="form-control" id="productKeyFilter" placeholder="产品密钥...">
                            </div>

                            <!-- 高级筛选按钮 -->
                            <button class="btn btn-outline-secondary" type="button" onclick="toggleAdvancedFilters()" title="展开更多筛选选项">
                                <i class="fas fa-sliders-h me-1"></i>高级筛选
                            </button>
                        </div>

                        <!-- 搜索框 -->
                        <div class="input-group" style="width: 280px;">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索设备ID或备注...">
                            <button class="btn btn-outline-secondary" type="button" title="搜索">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 高级筛选行（默认隐藏） -->
                    <div id="advancedFilters" class="collapse">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light border-0">
                                <h6 class="mb-0 text-muted">
                                    <i class="fas fa-filter me-2"></i>高级筛选选项
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label small fw-medium">设备备注筛选</label>
                                        <input type="text" class="form-control form-control-sm" id="remarkFilter" placeholder="输入设备备注关键词...">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label small fw-medium">调试状态</label>
                                        <select class="form-select form-select-sm" id="debugStatusFilter">
                                            <option value="all">所有调试状态</option>
                                            <option value="enabled">调试运行中</option>
                                            <option value="disabled">调试已停止</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label small fw-medium">快速筛选</label>
                                        <select class="form-select form-select-sm" id="quickFilter">
                                            <option value="all">全部设备</option>
                                            <option value="today_created">今日新增</option>
                                            <option value="long_offline">长期离线(7天+)</option>
                                            <option value="upgrade_failed">升级失败</option>
                                            <option value="no_remark">无备注设备</option>
                                        </select>
                                    </div>
                                </div>
                            <div class="row g-3 mt-2">
                                <div class="col-md-4">
                                    <label class="form-label small">创建时间范围</label>
                                    <div class="input-group input-group-sm">
                                        <input type="date" class="form-control" id="createDateStart">
                                        <span class="input-group-text">至</span>
                                        <input type="date" class="form-control" id="createDateEnd">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label small">最后在线时间范围</label>
                                    <div class="input-group input-group-sm">
                                        <input type="date" class="form-control" id="onlineDateStart">
                                        <span class="input-group-text">至</span>
                                        <input type="date" class="form-control" id="onlineDateEnd">
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <div class="btn-group w-100">
                                        <button type="button" class="btn btn-sm btn-primary" onclick="applyAdvancedFilters()">
                                            <i class="fas fa-filter me-1"></i>应用筛选
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllFilters()">
                                            <i class="fas fa-times me-1"></i>清除筛选
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <!-- 加载状态 -->
            <div id="loadingIndicator" class="text-center p-4" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载设备列表...</div>
            </div>

            <div class="table-responsive position-relative">
                <style>
                    /* 设备表格优化样式 */
                    .device-table-container {
                        position: relative;
                    }

                    .device-table {
                        margin-bottom: 0;
                        font-size: 0.9rem; /* 稍微减小字体以提高信息密度 */
                    }

                    .device-table th,
                    .device-table td {
                        white-space: nowrap;
                        vertical-align: middle;
                        padding: 0.75rem 0.5rem; /* 优化行高和间距 */
                        border-bottom: 1px solid #f0f0f0; /* 更淡的分隔线 */
                    }

                    .device-table th {
                        font-weight: 600;
                        font-size: 0.85rem;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        color: #6c757d;
                        background-color: #f8f9fa;
                        border-bottom: 2px solid #dee2e6;
                    }

                    /* 设备行悬停效果 */
                    .device-table tbody tr:hover {
                        background-color: #f8f9fa;
                        transform: translateY(-1px);
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        transition: all 0.2s ease;
                    }

                    /* 设备ID列样式 */
                    .device-id-cell {
                        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                        font-weight: 600;
                        color: #495057;
                    }

                    /* 设备备注样式 */
                    .device-remark-cell {
                        max-width: 150px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        color: #6c757d;
                        font-style: italic;
                    }

                    /* 固定操作列 */
                    .device-table .action-column {
                        position: sticky;
                        right: 0;
                        background-color: white;
                        border-left: 1px solid #dee2e6;
                        box-shadow: -2px 0 5px rgba(0,0,0,0.1);
                        z-index: 10;
                        min-width: 300px;
                        width: 300px;
                    }

                    /* 操作按钮容器 */
                    .action-buttons-container {
                        display: flex;
                        gap: 4px;
                        overflow-x: auto;
                        padding: 2px;
                        scrollbar-width: thin;
                        scrollbar-color: #ccc transparent;
                    }

                    .action-buttons-container::-webkit-scrollbar {
                        height: 4px;
                    }

                    .action-buttons-container::-webkit-scrollbar-track {
                        background: transparent;
                    }

                    .action-buttons-container::-webkit-scrollbar-thumb {
                        background: #ccc;
                        border-radius: 2px;
                    }

                    .action-buttons-container::-webkit-scrollbar-thumb:hover {
                        background: #999;
                    }

                    /* 操作按钮样式 */
                    .action-btn {
                        padding: 0.25rem 0.5rem;
                        font-size: 0.75rem;
                        border-radius: 4px;
                        border: none;
                        color: white;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        white-space: nowrap;
                        min-width: 60px;
                        text-align: center;
                        text-decoration: none;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        gap: 0.25rem;
                    }

                    .action-btn:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                        color: white;
                        text-decoration: none;
                    }

                    /* 不同操作按钮的颜色 */
                    .action-btn-upgrade {
                        background: linear-gradient(45deg, #007bff, #0056b3);
                    }

                    .action-btn-edit {
                        background: linear-gradient(45deg, #ffc107, #e0a800);
                    }

                    .action-btn-config {
                        background: linear-gradient(45deg, #6f42c1, #5a2d91);
                    }

                    .action-btn-ai {
                        background: linear-gradient(45deg, #20c997, #17a2b8);
                    }

                    .action-btn-params {
                        background: linear-gradient(45deg, #fd7e14, #e55a00);
                    }

                    .action-btn-delete {
                        background: linear-gradient(45deg, #dc3545, #c82333);
                    }

                    .action-btn-console {
                        background: linear-gradient(45deg, #17a2b8, #138496);
                    }

                    .device-table thead .action-column {
                        background-color: var(--bs-light);
                    }

                    /* 响应式隐藏列 */
                    @media (max-width: 1400px) {
                        .device-table .hide-xl { display: none; }
                    }

                    @media (max-width: 1200px) {
                        .device-table .hide-lg { display: none; }
                    }

                    @media (max-width: 992px) {
                        .device-table .hide-md { display: none; }
                    }

                    @media (max-width: 768px) {
                        .device-table .hide-sm { display: none; }
                    }

                    /* 紧凑的按钮样式 */
                    .btn-compact {
                        padding: 0.25rem 0.4rem;
                        font-size: 0.75rem;
                        margin: 0 1px;
                    }

                    /* 状态徽章样式 */
                    .status-badge {
                        font-size: 0.7rem;
                        padding: 0.3em 0.6em;
                        border-radius: 12px;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }

                    /* 在线状态特殊样式 */
                    .status-online {
                        background: linear-gradient(45deg, #28a745, #20c997);
                        color: white;
                        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
                    }

                    .status-offline {
                        background: linear-gradient(45deg, #dc3545, #fd7e14);
                        color: white;
                        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
                    }

                    /* 产品密钥样式 */
                    .product-key-cell {
                        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                        font-size: 0.8rem;
                        background-color: #f8f9fa;
                        padding: 0.25rem 0.5rem;
                        border-radius: 4px;
                        border: 1px solid #e9ecef;
                        color: #495057;
                    }

                    /* 时间显示样式 */
                    .time-cell {
                        font-size: 0.8rem;
                        color: #6c757d;
                    }

                    .time-cell i {
                        opacity: 0.7;
                        margin-right: 0.25rem;
                    }

                    /* ===== 暗黑模式样式 ===== */
                    body.dark-mode .device-table {
                        background-color: #2d3748;
                        color: #e2e8f0;
                    }

                    body.dark-mode .device-table th {
                        background-color: #1a202c;
                        color: #a0aec0;
                        border-bottom: 2px solid #4a5568;
                    }

                    body.dark-mode .device-table td {
                        border-bottom: 1px solid #4a5568;
                    }

                    body.dark-mode .device-table tbody tr:hover {
                        background-color: #2d3748;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    }

                    /* 暗黑模式下的固定操作列 */
                    body.dark-mode .device-table .action-column {
                        background-color: #2d3748;
                        border-left: 1px solid #4a5568;
                        box-shadow: -2px 0 5px rgba(0,0,0,0.3);
                    }

                    body.dark-mode .device-table thead .action-column {
                        background-color: #1a202c;
                    }

                    /* 暗黑模式下的操作按钮 */
                    body.dark-mode .action-buttons-container::-webkit-scrollbar-thumb {
                        background: #4a5568;
                    }

                    body.dark-mode .action-buttons-container::-webkit-scrollbar-thumb:hover {
                        background: #718096;
                    }

                    body.dark-mode .action-btn-upgrade {
                        background: linear-gradient(45deg, #4299e1, #3182ce);
                    }

                    body.dark-mode .action-btn-edit {
                        background: linear-gradient(45deg, #ed8936, #dd6b20);
                    }

                    body.dark-mode .action-btn-config {
                        background: linear-gradient(45deg, #805ad5, #6b46c1);
                    }

                    body.dark-mode .action-btn-ai {
                        background: linear-gradient(45deg, #38b2ac, #319795);
                    }

                    body.dark-mode .action-btn-params {
                        background: linear-gradient(45deg, #f56500, #ea580c);
                    }

                    body.dark-mode .action-btn-delete {
                        background: linear-gradient(45deg, #e53e3e, #c53030);
                    }

                    body.dark-mode .action-btn-console {
                        background: linear-gradient(45deg, #4fd1c7, #38b2ac);
                    }

                    /* 暗黑模式下的设备ID样式 */
                    body.dark-mode .device-id-cell {
                        color: #e2e8f0;
                    }

                    /* 暗黑模式下的设备备注样式 */
                    body.dark-mode .device-remark-cell {
                        color: #a0aec0;
                    }

                    /* 暗黑模式下的产品密钥样式 */
                    body.dark-mode .product-key-cell {
                        background-color: #1a202c;
                        border: 1px solid #4a5568;
                        color: #e2e8f0;
                    }

                    /* 暗黑模式下的时间显示样式 */
                    body.dark-mode .time-cell {
                        color: #a0aec0;
                    }

                    /* 暗黑模式下的状态徽章 */
                    body.dark-mode .status-online {
                        background: linear-gradient(45deg, #38a169, #48bb78);
                        box-shadow: 0 2px 4px rgba(56, 161, 105, 0.4);
                    }

                    body.dark-mode .status-offline {
                        background: linear-gradient(45deg, #e53e3e, #fc8181);
                        box-shadow: 0 2px 4px rgba(229, 62, 62, 0.4);
                    }

                    /* 暗黑模式下的筛选区域 */
                    body.dark-mode .card {
                        background-color: #2d3748;
                        border: 1px solid #4a5568;
                    }

                    body.dark-mode .card-header {
                        background-color: #1a202c;
                        border-bottom: 1px solid #4a5568;
                        color: #e2e8f0;
                    }

                    body.dark-mode .form-control,
                    body.dark-mode .form-select {
                        background-color: #1a202c;
                        border: 1px solid #4a5568;
                        color: #e2e8f0;
                    }

                    body.dark-mode .form-control:focus,
                    body.dark-mode .form-select:focus {
                        background-color: #1a202c;
                        border-color: #4299e1;
                        box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
                        color: #e2e8f0;
                    }

                    body.dark-mode .input-group-text {
                        background-color: #1a202c;
                        border: 1px solid #4a5568;
                        color: #a0aec0;
                    }

                    /* 选中设备卡片样式 */
                    #selectedDevicesCard {
                        animation: slideDown 0.3s ease-out;
                    }

                    @keyframes slideDown {
                        from {
                            opacity: 0;
                            transform: translateY(-20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    @keyframes slideUp {
                        from {
                            opacity: 1;
                            transform: translateY(0);
                        }
                        to {
                            opacity: 0;
                            transform: translateY(-20px);
                        }
                    }

                    .selected-device-item {
                        transition: all 0.3s ease;
                        border: 1px solid #dee2e6;
                        border-radius: 8px;
                        background: #f8f9fa;
                        margin-bottom: 10px;
                        padding: 12px;
                        position: relative;
                        overflow: hidden;
                    }

                    .selected-device-item:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        border-color: #0d6efd;
                    }

                    .selected-device-item .device-info {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }

                    .selected-device-item .device-details {
                        flex: 1;
                        border-radius: 4px;
                        padding: 4px;
                        transition: background-color 0.2s ease;
                    }

                    .selected-device-item .device-details:hover {
                        background-color: rgba(13, 110, 253, 0.1);
                    }

                    .selected-device-item .device-id {
                        font-weight: 600;
                        color: #0d6efd;
                        font-size: 0.95rem;
                    }

                    .selected-device-item .device-remark {
                        color: #6c757d;
                        font-size: 0.85rem;
                        margin-top: 2px;
                    }

                    .selected-device-item .device-product-key {
                        color: #198754;
                        font-size: 0.8rem;
                        font-family: 'Courier New', monospace;
                        margin-top: 2px;
                    }

                    .selected-device-item .remove-btn {
                        opacity: 0.7;
                        transition: opacity 0.2s ease;
                    }

                    .selected-device-item:hover .remove-btn {
                        opacity: 1;
                    }

                    /* 暗黑模式下的选中设备卡片样式 */
                    body.dark-mode .selected-device-item {
                        background: #2d3748;
                        border-color: #4a5568;
                        color: #e2e8f0;
                    }

                    body.dark-mode .selected-device-item:hover {
                        border-color: #4299e1;
                        box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
                    }

                    body.dark-mode .selected-device-item .device-id {
                        color: #4299e1;
                    }

                    body.dark-mode .selected-device-item .device-remark {
                        color: #a0aec0;
                    }

                    body.dark-mode .selected-device-item .device-product-key {
                        color: #68d391;
                    }

                    body.dark-mode .selected-device-item .device-details:hover {
                        background-color: rgba(66, 153, 225, 0.1);
                    }

                    /* 设备项淡入动画 */
                    .device-item-enter {
                        animation: deviceItemEnter 0.4s ease-out;
                    }

                    @keyframes deviceItemEnter {
                        from {
                            opacity: 0;
                            transform: scale(0.9) translateY(10px);
                        }
                        to {
                            opacity: 1;
                            transform: scale(1) translateY(0);
                        }
                    }

                    /* 设备项淡出动画 */
                    .device-item-exit {
                        animation: deviceItemExit 0.3s ease-in forwards;
                    }

                    @keyframes deviceItemExit {
                        from {
                            opacity: 1;
                            transform: scale(1) translateY(0);
                        }
                        to {
                            opacity: 0;
                            transform: scale(0.9) translateY(-10px);
                        }
                    }

                    /* 暗黑模式下的按钮样式 */
                    body.dark-mode .btn-outline-primary {
                        color: #4299e1;
                        border-color: #4299e1;
                    }

                    body.dark-mode .btn-outline-primary:hover {
                        background-color: #4299e1;
                        border-color: #4299e1;
                        color: #1a202c;
                    }

                    body.dark-mode .btn-outline-warning {
                        color: #ed8936;
                        border-color: #ed8936;
                    }

                    body.dark-mode .btn-outline-warning:hover {
                        background-color: #ed8936;
                        border-color: #ed8936;
                        color: #1a202c;
                    }

                    body.dark-mode .btn-outline-secondary {
                        color: #a0aec0;
                        border-color: #4a5568;
                    }

                    body.dark-mode .btn-outline-secondary:hover {
                        background-color: #4a5568;
                        border-color: #4a5568;
                        color: #e2e8f0;
                    }

                    /* 暗黑模式下的模态框样式 */
                    body.dark-mode .modal-content {
                        background-color: #2d3748;
                        border: 1px solid #4a5568;
                    }

                    body.dark-mode .modal-header {
                        border-bottom: 1px solid #4a5568;
                    }

                    body.dark-mode .modal-footer {
                        border-top: 1px solid #4a5568;
                    }

                    body.dark-mode .modal-title {
                        color: #e2e8f0;
                    }

                    body.dark-mode .list-group-item {
                        background-color: #1a202c;
                        border: 1px solid #4a5568;
                        color: #e2e8f0;
                    }

                    body.dark-mode .alert-info {
                        background-color: #2c5282;
                        border-color: #3182ce;
                        color: #bee3f8;
                    }

                    body.dark-mode .alert-warning {
                        background-color: #975a16;
                        border-color: #d69e2e;
                        color: #faf089;
                    }

                    body.dark-mode .alert-success {
                        background-color: #276749;
                        border-color: #38a169;
                        color: #9ae6b4;
                    }

                    body.dark-mode .alert-danger {
                        background-color: #742a2a;
                        border-color: #e53e3e;
                        color: #fed7d7;
                    }

                    /* 分页按钮优化样式 */
                    .pagination-lg .page-link {
                        padding: 0.75rem 1rem;
                        font-size: 1rem;
                        min-width: 48px;
                        min-height: 48px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 0.375rem;
                        margin: 0 2px;
                        transition: all 0.2s ease-in-out;
                    }

                    .pagination-lg .page-link:hover {
                        background-color: #e9ecef;
                        border-color: #dee2e6;
                        transform: translateY(-1px);
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }

                    .pagination-lg .page-item.active .page-link {
                        background-color: #0d6efd;
                        border-color: #0d6efd;
                        box-shadow: 0 2px 4px rgba(13,110,253,0.25);
                    }

                    .pagination-lg .page-item.disabled .page-link {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }

                    /* 分页按钮图标样式 */
                    .pagination-lg .page-link i {
                        font-size: 1.1rem;
                    }

                    /* 响应式分页 */
                    @media (max-width: 768px) {
                        .pagination-lg .page-link {
                            padding: 0.5rem 0.75rem;
                            min-width: 40px;
                            min-height: 40px;
                            font-size: 0.9rem;
                        }
                    }

                    /* 深色模式下的分页样式 */
                    body.dark-mode .pagination-lg .page-link {
                        background-color: #495057;
                        border-color: #6c757d;
                        color: #fff;
                    }

                    body.dark-mode .pagination-lg .page-link:hover {
                        background-color: #6c757d;
                        border-color: #adb5bd;
                    }

                    body.dark-mode .pagination-lg .page-item.active .page-link {
                        background-color: #0d6efd;
                        border-color: #0d6efd;
                    }
                </style>

                <table class="table table-hover device-table">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 40px;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllDevices">
                                </div>
                            </th>
                            <th style="min-width: 100px;">设备ID</th>
                            <th style="min-width: 120px;" class="hide-sm">设备备注</th>
                            <th style="width: 70px;">状态</th>
                            <th style="min-width: 100px;" class="hide-md">产品密钥</th>
                            <th style="min-width: 100px;" class="hide-lg">设备类型</th>
                            <th style="min-width: 80px;" class="hide-lg">固件版本</th>
                            <th style="width: 80px;" class="hide-xl">升级状态</th>
                            <th style="min-width: 130px;" class="hide-lg">最后升级时间</th>
                            <th style="min-width: 130px;" class="hide-md">最后在线时间</th>
                            <th style="width: 80px;" class="hide-xl">调试状态</th>
                            <th class="action-column text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody id="deviceTableBody">
                        <!-- 设备列表将通过Ajax动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top flex-wrap gap-3">
                <div class="d-flex align-items-center gap-3">
                    <div class="text-muted">
                        <span id="paginationInfo">显示第 1-20 条，共 0 条记录</span>
                    </div>

                    <!-- 每页显示数量配置 -->
                    <div class="d-flex align-items-center gap-2">
                        <span class="text-muted small">每页显示</span>
                        <select class="form-select form-select-sm" id="pageSizeSelect" style="width: 80px;">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span class="text-muted small">条</span>
                    </div>
                </div>

                <!-- 页码跳转输入框 -->
                <div class="d-flex align-items-center gap-2">
                    <span class="text-muted small">跳转到</span>
                    <div class="input-group input-group-sm" style="width: 120px;">
                        <input type="number" class="form-control" id="pageJumpInput" min="1" placeholder="页码">
                        <button class="btn btn-outline-secondary" type="button" onclick="jumpToPage()">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                    <span class="text-muted small">共 <span id="totalPagesSpan">0</span> 页</span>
                </div>

                <nav aria-label="设备列表分页">
                    <ul class="pagination pagination-lg mb-0" id="paginationControls">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 选中设备展示卡片 -->
    <div class="row mt-4" id="selectedDevicesCard" style="display: none;">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary bg-opacity-10 border-primary">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-primary">
                            <i class="fas fa-check-circle me-2"></i>已选设备
                            <span class="badge bg-primary ms-2" id="selectedDevicesCardCount">0</span>
                        </h6>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllSelectedDevices()">
                            <i class="fas fa-times me-1"></i>清空选择
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row" id="selectedDevicesContainer">
                        <!-- 选中的设备将在这里显示 -->
                    </div>
                    <div class="text-center mt-3" id="selectedDevicesLoading" style="display: none;">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span class="ms-2 text-muted">正在加载设备信息...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加设备模态框 -->
<div class="modal fade" id="addDeviceModal" tabindex="-1" aria-labelledby="addDeviceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDeviceModalLabel">
                    <i class="fas fa-plus-circle text-primary me-2"></i>添加设备
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addDeviceForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="device_id" class="form-label">设备ID</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-microchip"></i></span>
                            <input type="text" class="form-control" id="device_id" name="device_id" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="device_remark" class="form-label">设备备注</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            <input type="text" class="form-control" id="device_remark" name="device_remark">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="product_key" class="form-label">产品密钥</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-key"></i></span>
                            <input type="text" class="form-control" id="product_key" name="product_key" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="device_type" class="form-label">设备类型</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-microchip"></i></span>
                            <select class="form-select" id="device_type" name="device_type">
                                <option value="">请选择设备类型</option>
                                <option value="10">V2 (旧版霍尔传感器版本，黑色PCB)</option>
                                <option value="50">V5 (新版BL0910 10通道版本)</option>
                                <option value="51">V51 (新版BL0939 2通道版本)</option>
                            </select>
                        </div>
                        <div class="form-text">可选，用于批量升级时的固件匹配</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>添加
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量导入模态框 -->
<div class="modal fade" id="batchImportModal" tabindex="-1" aria-labelledby="batchImportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchImportModalLabel">
                    <i class="fas fa-file-import text-success me-2"></i>批量导入设备
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="batchImportForm" action="{{ url_for('device.batch_import_devices') }}" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import_file" class="form-label">选择Excel文件</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="import_file" name="import_file" accept=".xlsx,.xls" required>
                            <button class="btn btn-outline-secondary" type="button" id="downloadTemplate">
                                <i class="fas fa-download"></i> 下载模板
                            </button>
                        </div>
                        <div class="form-text">请使用Excel文件，包含设备ID、设备备注、产品密钥等字段</div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>导入说明：
                        <ul class="mb-0 mt-2">
                            <li>设备ID为必填项，且必须唯一</li>
                            <li>已存在的设备ID将被跳过</li>
                            <li>产品密钥为必填项</li>
                            <li>设备备注为选填项</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-file-import me-1"></i>开始导入
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 导入结果模态框 -->
<div class="modal fade" id="importResultModal" tabindex="-1" aria-labelledby="importResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importResultModalLabel">
                    <i class="fas fa-clipboard-check text-primary me-2"></i>导入结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h6 class="card-title">成功导入</h6>
                                <h3 class="mb-0" id="successCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h6 class="card-title">已存在</h6>
                                <h3 class="mb-0" id="existingCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h6 class="card-title">导入失败</h6>
                                <h3 class="mb-0" id="failedCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>状态</th>
                                <th>原因</th>
                            </tr>
                        </thead>
                        <tbody id="importResultTable">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>刷新列表
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量设置参数模态框 -->
<div class="modal fade" id="batchSetParametersModal" tabindex="-1" aria-labelledby="batchSetParametersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchSetParametersModalLabel">
                    <i class="fas fa-cogs text-primary me-2"></i>批量设置参数
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="batchSetParametersForm">
                    <!-- 选中的设备列表 -->
                    <div class="mb-4">
                        <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                        <div class="list-group" id="selectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                            <!-- 设备列表将通过JavaScript动态填充 -->
                        </div>
                    </div>

                    <!-- 参数设置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>批量参数设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>参数设置</h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="batchParamName" class="form-label">参数名称</label>
                                        <select class="form-select" id="batchParamName" required>
                                            <option value="">请选择参数...</option>
                                            <optgroup label="时间参数">
                                                <option value="REG_T1">REG_T1 - 长时间未插入充电器检测时间</option>
                                                <option value="REG_T2">REG_T2 - 功率大于0连续时间</option>
                                                <option value="REG_T3">REG_T3 - 浮充时间</option>
                                                <option value="REG_T4">REG_T4 - 功率超过限制判定时间</option>
                                                <option value="REG_T5">REG_T5 - 总功率超过限制触发时间</option>
                                                <option value="REG_T6">REG_T6 - 温度超过阈值判定时间</option>
                                                <option value="REG_T7">REG_T7 - 初始单个口功率过大判定时间</option>
                                                <option value="REG_T8">REG_T8 - 充电过程中继电器开路状态判断时间</option>
                                                <option value="REG_T9">REG_T9 - 首次进入充电过程中功率突降为0时的浮充时间</option>
                                                <option value="REG_T10">REG_T10 - 无线充电浮充时间</option>
                                                <option value="REG_T11">REG_T11 - 拔出充电器的判定时间</option>
                                            </optgroup>
                                            <optgroup label="功率参数">
                                                <option value="REG_P1">REG_P1 - 浮充功率阈值</option>
                                                <option value="REG_P2">REG_P2 - 单口充电过程中的功率限制</option>
                                                <option value="REG_P3">REG_P3 - 单口充电过程中的安全功率限制</option>
                                                <option value="REG_P4">REG_P4 - 总功率限制</option>
                                                <option value="REG_P5">REG_P5 - 单口初始安全功率限制</option>
                                                <option value="REG_P6">REG_P6 - 启动充电后检测充电负载存在阈值</option>
                                                <option value="REG_P7">REG_P7 - 无线充电浮充功率阈值</option>
                                                <option value="REG_P8">REG_P8 - 判断是否接入用电设备的阈值</option>
                                            </optgroup>
                                            <optgroup label="控制和状态参数">
                                                <option value="REG_CTRL1">REG_CTRL1 - 控制寄存器</option>
                                                <option value="REG_TEMP1">REG_TEMP1 - 过温保护阈值</option>
                                                <option value="REG_PERSENTAGE">REG_PERSENTAGE - 拔出插头判定百分比</option>
                                                <option value="REG_CSQ">REG_CSQ - 信号强度和误码率</option>
                                            </optgroup>
                                            <optgroup label="位置参数">
                                                <option value="REG_LOCATION_CODE">REG_LOCATION_CODE - 位置编码</option>
                                                <option value="REG_LOCATION_LATITUDE_H">REG_LOCATION_LATITUDE_H - 纬度高字节</option>
                                                <option value="REG_LOCATION_LATITUDE_L">REG_LOCATION_LATITUDE_L - 纬度低字节</option>
                                                <option value="REG_LOCATION_LONGITUDE_H">REG_LOCATION_LONGITUDE_H - 经度高字节</option>
                                                <option value="REG_LOCATION_LONGITUDE_L">REG_LOCATION_LONGITUDE_L - 经度低字节</option>
                                            </optgroup>
                                            <optgroup label="错误计数和保护">
                                                <option value="REG_ERROR_CNT1">REG_ERROR_CNT1 - 临时错误计数器1</option>
                                                <option value="REG_ERROR_CNT2">REG_ERROR_CNT2 - 临时错误计数器2</option>
                                                <option value="REG_UID_PROTECT_KEY1">REG_UID_PROTECT_KEY1 - UID保护密钥1</option>
                                                <option value="REG_UID_PROTECT_KEY2">REG_UID_PROTECT_KEY2 - UID保护密钥2</option>
                                            </optgroup>
                                            <optgroup label="系统配置">
                                                <option value="REG_HEART_AND_BILLING_PROTO_TYPE">REG_HEART_AND_BILLING_PROTO_TYPE -
                                                    MQTT服务器类型</option>
                                                <option value="REG_RESERV3">REG_RESERV3 - 保留3</option>
                                                <option value="REG_RESERV4">REG_RESERV4 - 保留4</option>
                                                <option value="REG_FACTORY_FAULT">REG_FACTORY_FAULT - 工厂故障记录1</option>
                                                <option value="REG_FACTORY_FAULT2">REG_FACTORY_FAULT2 - 工厂故障记录2</option>
                                            </optgroup>
                                            <optgroup label="插座0功率阈值">
                                                <option value="REG_P2_PLUG0">REG_P2_PLUG0 - 插座0的P2功率阈值</option>
                                                <option value="REG_P3_PLUG0">REG_P3_PLUG0 - 插座0的P3功率阈值</option>
                                                <option value="REG_P5_PLUG0">REG_P5_PLUG0 - 插座0的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座1功率阈值">
                                                <option value="REG_P2_PLUG1">REG_P2_PLUG1 - 插座1的P2功率阈值</option>
                                                <option value="REG_P3_PLUG1">REG_P3_PLUG1 - 插座1的P3功率阈值</option>
                                                <option value="REG_P5_PLUG1">REG_P5_PLUG1 - 插座1的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座2功率阈值">
                                                <option value="REG_P2_PLUG2">REG_P2_PLUG2 - 插座2的P2功率阈值</option>
                                                <option value="REG_P3_PLUG2">REG_P3_PLUG2 - 插座2的P3功率阈值</option>
                                                <option value="REG_P5_PLUG2">REG_P5_PLUG2 - 插座2的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座3功率阈值">
                                                <option value="REG_P2_PLUG3">REG_P2_PLUG3 - 插座3的P2功率阈值</option>
                                                <option value="REG_P3_PLUG3">REG_P3_PLUG3 - 插座3的P3功率阈值</option>
                                                <option value="REG_P5_PLUG3">REG_P5_PLUG3 - 插座3的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座4功率阈值">
                                                <option value="REG_P2_PLUG4">REG_P2_PLUG4 - 插座4的P2功率阈值</option>
                                                <option value="REG_P3_PLUG4">REG_P3_PLUG4 - 插座4的P3功率阈值</option>
                                                <option value="REG_P5_PLUG4">REG_P5_PLUG4 - 插座4的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座5功率阈值">
                                                <option value="REG_P2_PLUG5">REG_P2_PLUG5 - 插座5的P2功率阈值</option>
                                                <option value="REG_P3_PLUG5">REG_P3_PLUG5 - 插座5的P3功率阈值</option>
                                                <option value="REG_P5_PLUG5">REG_P5_PLUG5 - 插座5的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座6功率阈值">
                                                <option value="REG_P2_PLUG6">REG_P2_PLUG6 - 插座6的P2功率阈值</option>
                                                <option value="REG_P3_PLUG6">REG_P3_PLUG6 - 插座6的P3功率阈值</option>
                                                <option value="REG_P5_PLUG6">REG_P5_PLUG6 - 插座6的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座7功率阈值">
                                                <option value="REG_P2_PLUG7">REG_P2_PLUG7 - 插座7的P2功率阈值</option>
                                                <option value="REG_P3_PLUG7">REG_P3_PLUG7 - 插座7的P3功率阈值</option>
                                                <option value="REG_P5_PLUG7">REG_P5_PLUG7 - 插座7的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座8功率阈值">
                                                <option value="REG_P2_PLUG8">REG_P2_PLUG8 - 插座8的P2功率阈值</option>
                                                <option value="REG_P3_PLUG8">REG_P3_PLUG8 - 插座8的P3功率阈值</option>
                                                <option value="REG_P5_PLUG8">REG_P5_PLUG8 - 插座8的P5功率阈值</option>
                                            </optgroup>
                                            <optgroup label="插座9功率阈值">
                                                <option value="REG_P2_PLUG9">REG_P2_PLUG9 - 插座9的P2功率阈值</option>
                                                <option value="REG_P3_PLUG9">REG_P3_PLUG9 - 插座9的P3功率阈值</option>
                                                <option value="REG_P5_PLUG9">REG_P5_PLUG9 - 插座9的P5功率阈值</option>
                                            </optgroup>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="batchParamValue" class="form-label">参数值</label>
                                        <input type="number" class="form-control" id="batchParamValue" required>
                                        <div class="form-text" id="paramDescription">请选择参数查看描述信息</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 设置记录 -->
                    <div class="mb-4">
                        <h6 class="mb-3"><i class="fas fa-history me-2"></i>设置记录</h6>
                        <!-- 总体设置结果 -->
                        <div class="alert alert-info mb-3" id="batchSetSummary" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle me-2"></i>
                                <div>
                                    <strong>总体设置结果：</strong>
                                    <span id="batchSetSummaryText"></span>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>设备ID</th>
                                        <th>设备备注</th>
                                        <th>参数名称</th>
                                        <th>设置值</th>
                                        <th>状态</th>
                                        <th>错误信息</th>
                                    </tr>
                                </thead>
                                <tbody id="batchSetRecords">
                                    <!-- 设置记录将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="batchSetParameters()">
                    <i class="fas fa-save me-1"></i>保存设置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询参数模态框 -->
<div class="modal fade" id="batchQueryParametersModal" tabindex="-1" aria-labelledby="batchQueryParametersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryParametersModalLabel">
                    <i class="fas fa-search text-warning me-2"></i>批量查询参数
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 选中的设备列表 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                    <div class="list-group" id="querySelectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                        <!-- 设备列表将通过JavaScript动态填充 -->
                    </div>
                </div>

                <!-- 查询结果 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-list-alt me-2"></i>查询结果</h6>
                    <div class="alert alert-info mb-3" id="batchQuerySummary" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>查询结果：</strong>
                                <span id="batchQuerySummaryText"></span>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>设备备注</th>
                                    <th>状态</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody id="batchQueryRecords">
                                <!-- 查询记录将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmBatchQueryParameters()">
                    <i class="fas fa-search me-1"></i>确认查询
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询位置模态框 -->
<div class="modal fade" id="batchQueryLocationsModal" tabindex="-1" aria-labelledby="batchQueryLocationsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryLocationsModalLabel">
                    <i class="fas fa-map-marker-alt text-secondary me-2"></i>批量查询位置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 选中的设备列表 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                    <div class="list-group" id="locationSelectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                        <!-- 设备列表将通过JavaScript动态填充 -->
                    </div>
                </div>

                <!-- 查询结果 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-list-alt me-2"></i>查询结果</h6>
                    <div class="alert alert-info mb-3" id="batchLocationSummary" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>查询结果：</strong>
                                <span id="batchLocationSummaryText"></span>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>设备备注</th>
                                    <th>位置代码</th>
                                    <th>纬度</th>
                                    <th>经度</th>
                                    <th>状态</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody id="batchLocationRecords">
                                <!-- 查询记录将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-secondary" onclick="confirmBatchQueryLocations()">
                    <i class="fas fa-map-marker-alt me-1"></i>确认查询
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询参数结果模态框 -->
<div class="modal fade" id="batchQueryParametersResultModal" tabindex="-1" aria-labelledby="batchQueryParametersResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryParametersResultModalLabel">
                    <i class="fas fa-search text-warning me-2"></i>批量查询参数结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-3" id="batchQueryParametersResultSummary">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>
                            <strong>查询结果：</strong>
                            <span id="batchQueryParametersResultSummaryText"></span>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>设备备注</th>
                                <th>状态</th>
                                <th>错误信息</th>
                            </tr>
                        </thead>
                        <tbody id="batchQueryParametersResultTable">
                            <!-- 查询结果将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询位置结果模态框 -->
<div class="modal fade" id="batchQueryLocationsResultModal" tabindex="-1" aria-labelledby="batchQueryLocationsResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryLocationsResultModalLabel">
                    <i class="fas fa-map-marker-alt text-secondary me-2"></i>批量查询位置结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-3" id="batchQueryLocationsResultSummary">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>
                            <strong>查询结果：</strong>
                            <span id="batchQueryLocationsResultSummaryText"></span>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>设备备注</th>
                                <th>位置代码</th>
                                <th>纬度</th>
                                <th>经度</th>
                                <th>状态</th>
                                <th>错误信息</th>
                            </tr>
                        </thead>
                        <tbody id="batchQueryLocationsResultTable">
                            <!-- 查询结果将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询固件信息模态框 -->
<div class="modal fade" id="batchQueryFirmwareInfoModal" tabindex="-1" aria-labelledby="batchQueryFirmwareInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryFirmwareInfoModalLabel">
                    <i class="fas fa-microchip text-info me-2"></i>批量查询固件信息
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 选中的设备列表 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                    <div class="list-group" id="firmwareInfoSelectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                        <!-- 设备列表将通过JavaScript动态填充 -->
                    </div>
                </div>

                <!-- 查询结果 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-list-alt me-2"></i>查询结果</h6>
                    <div class="alert alert-info mb-3" id="batchQueryFirmwareInfoSummary" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>查询结果：</strong>
                                <span id="batchQueryFirmwareInfoSummaryText"></span>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>设备备注</th>
                                    <th>设备类型</th>
                                    <th>固件版本</th>
                                    <th>状态</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody id="batchQueryFirmwareInfoRecords">
                                <!-- 查询记录将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="executeBatchQueryFirmwareInfo()">
                    <i class="fas fa-search me-1"></i>开始查询
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量调试脚本模态框 -->
<div class="modal fade" id="batchDebugScriptModal" tabindex="-1" aria-labelledby="batchDebugScriptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDebugScriptModalLabel">
                    <i class="fas fa-terminal text-danger me-2"></i>批量调试脚本
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 选中的设备列表 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                    <div class="list-group" id="debugScriptSelectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                        <!-- 设备列表将通过JavaScript动态填充 -->
                    </div>
                </div>

                <!-- 调试脚本设置 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>调试脚本设置</h6>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="debugScriptFrequency" class="form-label">采样频率（秒）</label>
                            <input type="number" class="form-control" id="debugScriptFrequency" min="5" value="300" required>
                            <div class="form-text">最小采样频率为5秒</div>
                        </div>
                    </div>
                </div>

                <!-- 操作结果 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-history me-2"></i>操作结果</h6>
                    <div class="alert alert-info mb-3" id="batchDebugScriptSummary" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>操作结果：</strong>
                                <span id="batchDebugScriptSummaryText"></span>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>设备备注</th>
                                    <th>操作</th>
                                    <th>状态</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody id="batchDebugScriptRecords">
                                <!-- 操作记录将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="startBatchDebugScript()">
                    <i class="fas fa-play me-1"></i>启动脚本
                </button>
                <button type="button" class="btn btn-warning" onclick="stopBatchDebugScript()">
                    <i class="fas fa-stop me-1"></i>停止脚本
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量配置服务器模态框 -->
<div class="modal fade" id="batchServerConfigModal" tabindex="-1" aria-labelledby="batchServerConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchServerConfigModalLabel">
                    <i class="fas fa-server me-2"></i>批量配置设备服务器
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="batchServerConfigForm">
                    <!-- 选中设备数量显示 -->
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-info-circle me-2"></i>批量配置说明</h6>
                        <p class="mb-2">已选择 <strong id="batchSelectedCount">0</strong> 个设备进行批量配置</p>
                        <p class="mb-0">批量配置时需要指定源产品密钥和目标产品密钥，只有匹配源产品密钥的设备才会被配置</p>
                    </div>

                    <!-- 源配置 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="batchSourceProductKey" class="form-label">源产品密钥 <span class="text-danger">*</span></label>
                            <select class="form-select" id="batchSourceProductKey" required>
                                <option value="">请选择源产品...</option>
                            </select>
                            <div class="form-text">只有此产品密钥的设备会被配置</div>
                        </div>
                        <div class="col-md-6">
                            <label for="batchTargetProductKey" class="form-label">目标产品密钥 <span class="text-danger">*</span></label>
                            <select class="form-select" id="batchTargetProductKey" required>
                                <option value="">请先选择源产品...</option>
                            </select>
                            <div class="form-text">设备将配置为此产品密钥</div>
                        </div>
                    </div>

                    <!-- 配置预览 -->
                    <div class="alert alert-warning" id="batchConfigPreview" style="display: none;">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>配置预览</h6>
                        <div id="batchConfigDetails"></div>
                    </div>

                    <!-- 匹配设备预览 -->
                    <div class="card" id="batchMatchedDevices" style="display: none;">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>匹配的设备 (<span id="matchedDeviceCount">0</span>个)</h6>
                        </div>
                        <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                            <div id="matchedDeviceList"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchServerConfig" disabled>
                    <i class="fas fa-save me-2"></i>确认批量配置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量配置结果模态框 -->
<div class="modal fade" id="batchServerConfigResultModal" tabindex="-1" aria-labelledby="batchServerConfigResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchServerConfigResultModalLabel">
                    <i class="fas fa-server me-2"></i>批量配置结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 配置摘要 -->
                <div class="alert" id="batchConfigResultSummary">
                    <h6><i class="fas fa-info-circle me-2"></i>配置摘要</h6>
                    <div id="batchConfigResultSummaryText"></div>
                </div>

                <!-- 详细结果 -->
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>设备名称</th>
                                <th>状态</th>
                                <th>结果</th>
                            </tr>
                        </thead>
                        <tbody id="batchConfigResultTable">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 单设备服务器配置模态框 -->
<div class="modal fade" id="deviceServerConfigModal" tabindex="-1" aria-labelledby="deviceServerConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deviceServerConfigModalLabel">
                    <i class="fas fa-server me-2"></i>配置设备服务器
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="deviceServerConfigForm">
                    <!-- 设备信息显示 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">设备ID</label>
                            <input type="text" class="form-control" id="configDeviceId" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">当前产品密钥</label>
                            <input type="text" class="form-control" id="configCurrentProductKey" readonly>
                        </div>
                    </div>

                    <!-- 当前服务器信息 -->
                    <div class="alert alert-info" id="currentServerInfo">
                        <h6><i class="fas fa-info-circle me-2"></i>当前服务器信息</h6>
                        <div id="currentServerDetails"></div>
                    </div>

                    <!-- 目标配置选择 -->
                    <div class="mb-3">
                        <label for="targetProduct" class="form-label">目标产品 <span class="text-danger">*</span></label>
                        <select class="form-select" id="targetProduct" required>
                            <option value="">请选择目标产品...</option>
                        </select>
                        <div class="form-text">选择要迁移到的目标产品</div>
                    </div>

                    <!-- 新设备ID（可选） -->
                    <div class="mb-3">
                        <label for="newDeviceId" class="form-label">新设备ID（可选）</label>
                        <input type="text" class="form-control" id="newDeviceId" placeholder="留空则保持原设备ID不变">
                        <div class="form-text">通常情况下保持设备ID不变，仅在特殊需求时修改</div>
                    </div>

                    <!-- 新设备的密钥，针对转换到阿里云的情况 -->
                    <div class="mb-3">
                        <label for="newDeviceSecret" class="form-label">新设备的密钥，针对转换到阿里云的情况</label>
                        <input type="text" class="form-control" id="newDeviceSecret" placeholder="转换为阿里云产品时需要填写新设备的密钥">
                        <div class="form-text">仅转换为阿里云产品时需要填写新设备的密钥</div>
                    </div>

                    <!-- 迁移说明 -->
                    <div class="alert alert-warning" id="migrationDescription" style="display: none;">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>迁移说明</h6>
                        <div id="migrationDetails"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmDeviceServerConfig">
                    <i class="fas fa-save me-2"></i>确认配置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量修改产品密钥模态框 -->
<div class="modal fade" id="batchUpdateProductKeyModal" tabindex="-1" aria-labelledby="batchUpdateProductKeyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchUpdateProductKeyModalLabel">
                    <i class="fas fa-key text-info me-2"></i>批量修改产品密钥
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="batchUpdateProductKeyForm">
                    <!-- 选中的设备列表 -->
                    <div class="mb-4">
                        <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            已选择 <strong id="selectedDeviceCount">0</strong> 个设备进行批量修改
                        </div>
                        <div class="list-group" id="batchUpdateSelectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                            <!-- 设备列表将通过JavaScript动态填充 -->
                        </div>
                    </div>

                    <!-- 产品密钥设置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-key me-2"></i>产品密钥设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="newProductKey" class="form-label">新产品密钥 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="newProductKey" required placeholder="请输入新的产品密钥">
                                <div class="form-text">所有选中的设备将被修改为此产品密钥</div>
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>注意：</strong>批量修改产品密钥是不可逆操作，请确认新产品密钥正确无误。
                            </div>
                        </div>
                    </div>

                    <!-- 操作结果 -->
                    <div class="mb-4" id="batchUpdateResultSection" style="display: none;">
                        <h6 class="mb-3"><i class="fas fa-list-alt me-2"></i>操作结果</h6>
                        <div class="alert alert-info mb-3" id="batchUpdateSummary">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle me-2"></i>
                                <div>
                                    <strong>操作结果：</strong>
                                    <span id="batchUpdateSummaryText"></span>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>设备ID</th>
                                        <th>设备备注</th>
                                        <th>原产品密钥</th>
                                        <th>新产品密钥</th>
                                        <th>状态</th>
                                        <th>错误信息</th>
                                    </tr>
                                </thead>
                                <tbody id="batchUpdateRecords">
                                    <!-- 操作记录将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="confirmBatchUpdateProductKey">
                    <i class="fas fa-save me-1"></i>确认修改
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量调试脚本操作结果模态框 -->
<div class="modal fade" id="batchDebugScriptResultModal" tabindex="-1" aria-labelledby="batchDebugScriptResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDebugScriptResultModalLabel">
                    <i class="fas fa-list-alt me-2"></i>批量调试脚本操作结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center py-2">
                                    <h6 class="mb-1">总计</h6>
                                    <h4 class="mb-0" id="totalDevicesCount">0</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center py-2">
                                    <h6 class="mb-1">成功</h6>
                                    <h4 class="mb-0" id="successDevicesCount">0</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center py-2">
                                    <h6 class="mb-1">失败</h6>
                                    <h4 class="mb-0" id="failedDevicesCount">0</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center py-2">
                                    <h6 class="mb-1">跳过</h6>
                                    <h4 class="mb-0" id="skippedDevicesCount">0</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-sm table-hover">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th>设备ID</th>
                                <th>设备备注</th>
                                <th>操作类型</th>
                                <th>状态</th>
                                <th>结果信息</th>
                            </tr>
                        </thead>
                        <tbody id="batchDebugScriptResults">
                            <!-- 操作结果将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <div id="batchDebugScriptProgress" class="mt-3" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>操作进度</span>
                        <span id="progressText">0/0</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" id="progressBar">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="exportResultsBtn" style="display: none;">
                    <i class="fas fa-download me-1"></i>导出结果
                </button>
            </div>
        </div>
    </div>
</div>

{% include 'components/ota_modal.html' %}
{% include 'components/batch_ota_modal.html' %}

{% endblock %}

{% block scripts %}
<script>
// 全局变量
let currentPage = 1;
let currentFilters = {};
let selectedDevices = new Set(); // 存储选中的设备ID
let currentPageSize = 20; // 当前每页显示数量

// 设备选择状态管理器
const DeviceSelectionManager = {
    STORAGE_KEY: 'device_selection_state',

    // 从sessionStorage加载选择状态
    loadSelection: function() {
        try {
            const stored = sessionStorage.getItem(this.STORAGE_KEY);
            if (stored) {
                const data = JSON.parse(stored);
                selectedDevices = new Set(data.selectedDevices || []);
                this.updateSelectionDisplay();
                console.log(`已加载 ${selectedDevices.size} 个选中设备`);
            }
        } catch (error) {
            console.error('加载设备选择状态失败:', error);
            selectedDevices = new Set();
        }
    },

    // 保存选择状态到sessionStorage
    saveSelection: function() {
        try {
            const data = {
                selectedDevices: Array.from(selectedDevices),
                timestamp: Date.now()
            };
            sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
        } catch (error) {
            console.error('保存设备选择状态失败:', error);
        }
    },

    // 添加设备到选择列表
    addDevice: function(deviceId) {
        selectedDevices.add(deviceId);
        this.saveSelection();
        this.updateSelectionDisplay();
    },

    // 从选择列表移除设备
    removeDevice: function(deviceId) {
        selectedDevices.delete(deviceId);
        this.saveSelection();
        this.updateSelectionDisplay();
    },

    // 清空选择
    clearSelection: function() {
        selectedDevices.clear();
        this.saveSelection();
        this.updateSelectionDisplay();
    },

    // 更新选择显示
    updateSelectionDisplay: function() {
        // 更新选中设备计数
        const countElement = document.getElementById('selectedDevicesCount');
        if (countElement) {
            countElement.textContent = selectedDevices.size;
        }

        // 更新选中设备卡片计数
        const cardCountElement = document.getElementById('selectedDevicesCardCount');
        if (cardCountElement) {
            cardCountElement.textContent = selectedDevices.size;
        }

        // 显示或隐藏选中设备卡片
        this.updateSelectedDevicesCard();

        // 更新当前页面的复选框状态
        this.updateCurrentPageCheckboxes();

        // 更新全选复选框状态
        this.updateSelectAllCheckbox();
    },

    // 更新当前页面的复选框状态
    updateCurrentPageCheckboxes: function() {
        const checkboxes = document.querySelectorAll('.device-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectedDevices.has(checkbox.value);
        });
    },

    // 更新全选复选框状态
    updateSelectAllCheckbox: function() {
        const selectAllCheckbox = document.getElementById('selectAllDevices');
        if (!selectAllCheckbox) return;

        const currentPageCheckboxes = document.querySelectorAll('.device-checkbox');

        if (currentPageCheckboxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
            return;
        }

        const checkedCount = Array.from(currentPageCheckboxes).filter(cb => cb.checked).length;

        if (checkedCount === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCount === currentPageCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    },

    // 获取选中设备的详细信息（用于显示）
    getSelectedDevicesInfo: async function() {
        if (selectedDevices.size === 0) {
            return [];
        }

        try {
            const response = await fetch('/api/devices/batch_info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    device_ids: Array.from(selectedDevices)
                })
            });

            if (response.ok) {
                const data = await response.json();
                return data.devices || [];
            }
        } catch (error) {
            console.error('获取设备信息失败:', error);
        }

        // 如果API调用失败，返回基本信息
        return Array.from(selectedDevices).map(id => ({
            id: id,
            device_id: `设备 ${id}`,
            device_remark: '(信息获取失败)'
        }));
    },

    // 更新选中设备卡片显示
    updateSelectedDevicesCard: async function() {
        const card = document.getElementById('selectedDevicesCard');
        const container = document.getElementById('selectedDevicesContainer');
        const loading = document.getElementById('selectedDevicesLoading');

        if (selectedDevices.size === 0) {
            // 隐藏卡片
            if (card.style.display !== 'none') {
                card.style.animation = 'slideUp 0.3s ease-in';
                setTimeout(() => {
                    card.style.display = 'none';
                    card.style.animation = '';
                }, 300);
            }
            return;
        }

        // 显示卡片
        if (card.style.display === 'none') {
            card.style.display = 'block';
        }

        // 显示加载状态
        loading.style.display = 'block';
        container.innerHTML = '';

        try {
            // 获取设备详细信息
            const devicesInfo = await this.getSelectedDevicesInfo();

            // 隐藏加载状态
            loading.style.display = 'none';

            // 渲染设备项
            this.renderSelectedDeviceItems(devicesInfo);

        } catch (error) {
            console.error('更新选中设备卡片失败:', error);
            loading.style.display = 'none';
            container.innerHTML = '<div class="col-12"><div class="alert alert-warning">加载设备信息失败</div></div>';
        }
    },

    // 渲染选中设备项
    renderSelectedDeviceItems: function(devicesInfo) {
        const container = document.getElementById('selectedDevicesContainer');
        container.innerHTML = '';

        devicesInfo.forEach((device, index) => {
            const deviceCol = document.createElement('div');
            deviceCol.className = 'col-lg-4 col-md-6 col-12';

            const deviceItem = document.createElement('div');
            deviceItem.className = 'selected-device-item device-item-enter';
            deviceItem.style.animationDelay = `${index * 0.1}s`;

            deviceItem.innerHTML = `
                <div class="device-info">
                    <div class="device-details" onclick="locateDeviceInList('${device.device_id}')"
                         style="cursor: pointer;" title="点击定位到设备列表中的位置">
                        <div class="device-id">
                            <i class="fas fa-microchip me-1"></i>${device.device_id}
                        </div>
                        <div class="device-remark">
                            <i class="fas fa-tag me-1"></i>${device.device_remark || '无备注'}
                        </div>
                        <div class="device-product-key">
                            <i class="fas fa-key me-1"></i>${device.product_key || '未设置'}
                        </div>
                    </div>
                    <div class="device-actions">
                        <button type="button" class="btn btn-outline-primary btn-sm me-1"
                                onclick="locateDeviceInList('${device.device_id}')"
                                title="定位到列表">
                            <i class="fas fa-search"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm remove-btn"
                                onclick="removeSelectedDevice('${device.id}')"
                                title="移除选择">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;

            deviceCol.appendChild(deviceItem);
            container.appendChild(deviceCol);
        });
    }
};

// 清空所有选中设备
function clearAllSelectedDevices() {
    if (selectedDevices.size === 0) {
        showNotification('没有选中的设备', 'info');
        return;
    }

    if (confirm(`确定要清空所有 ${selectedDevices.size} 个选中的设备吗？`)) {
        DeviceSelectionManager.clearSelection();
        showNotification('已清空所有选中设备', 'success');
    }
}

// 移除单个选中设备
function removeSelectedDevice(deviceId) {
    const deviceElement = event.target.closest('.selected-device-item');

    // 添加退出动画
    if (deviceElement) {
        deviceElement.classList.add('device-item-exit');
        setTimeout(() => {
            DeviceSelectionManager.removeDevice(deviceId.toString());
            showNotification('已移除设备', 'success');
        }, 300);
    } else {
        DeviceSelectionManager.removeDevice(deviceId.toString());
        showNotification('已移除设备', 'success');
    }
}

// 定位设备在列表中的位置
function locateDeviceInList(deviceId) {
    console.log('定位设备:', deviceId);

    // 首先在当前页面查找设备
    const deviceRows = document.querySelectorAll('#deviceTableBody tr');
    let deviceFound = false;

    deviceRows.forEach(row => {
        const deviceIdCell = row.querySelector('td:nth-child(2)');
        if (deviceIdCell && deviceIdCell.textContent.trim() === deviceId) {
            // 找到设备，高亮显示
            row.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 添加高亮效果
            row.style.backgroundColor = '#fff3cd';
            row.style.border = '2px solid #ffc107';
            row.style.borderRadius = '4px';

            // 3秒后移除高亮
            setTimeout(() => {
                row.style.backgroundColor = '';
                row.style.border = '';
                row.style.borderRadius = '';
            }, 3000);

            deviceFound = true;
            showNotification(`已定位到设备 ${deviceId}`, 'success');
        }
    });

    if (!deviceFound) {
        // 如果当前页面没有找到，尝试搜索
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = deviceId;
            // 触发搜索
            applyFilters();
            showNotification(`正在搜索设备 ${deviceId}...`, 'info');
        } else {
            showNotification(`设备 ${deviceId} 不在当前页面，请使用搜索功能查找`, 'warning');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化设备选择管理器
    DeviceSelectionManager.loadSelection();

    initializeDeviceList();
    setupEventListeners();

    // 处理URL参数（来自OTA页面的快速操作）
    handleUrlParameters();
});

// 初始化设备列表
function initializeDeviceList() {
    loadDeviceStats();
    loadDeviceList(1);
}

// 通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 设置事件监听器
function setupEventListeners() {
    // 基础搜索和筛选事件（常用功能，实时响应）
    document.getElementById('searchInput').addEventListener('keyup', debounce(handleFilterChange, 500));
    document.getElementById('statusFilter').addEventListener('change', handleFilterChange);
    document.getElementById('firmwareFilter').addEventListener('keyup', debounce(handleFilterChange, 500));
    document.getElementById('otaStatusFilter').addEventListener('change', handleFilterChange);
    document.getElementById('productKeyFilter').addEventListener('keyup', debounce(handleFilterChange, 500));

    // 高级筛选事件（使用防抖，避免频繁请求）
    document.getElementById('remarkFilter').addEventListener('keyup', debounce(applyAdvancedFilters, 800));
    document.getElementById('debugStatusFilter').addEventListener('change', applyAdvancedFilters);
    document.getElementById('quickFilter').addEventListener('change', applyAdvancedFilters);

    // 日期筛选事件
    document.getElementById('createDateStart').addEventListener('change', applyAdvancedFilters);
    document.getElementById('createDateEnd').addEventListener('change', applyAdvancedFilters);
    document.getElementById('onlineDateStart').addEventListener('change', applyAdvancedFilters);
    document.getElementById('onlineDateEnd').addEventListener('change', applyAdvancedFilters);

    // 全选复选框事件
    document.getElementById('selectAllDevices').addEventListener('change', handleSelectAll);

    // 批量升级按钮事件
    const batchOtaBtn = document.querySelector('.batch-ota-btn');
    if (batchOtaBtn) {
        batchOtaBtn.addEventListener('click', showBatchOtaModalWithDeviceManager);
    }
}

// 全局模态框实例管理
let batchOtaModalInstance = null;

// 显示批量OTA升级模态框（使用DeviceSelectionManager）
async function showBatchOtaModalWithDeviceManager() {
    console.log('批量升级按钮被点击，当前选中设备数量:', selectedDevices.size);

    try {
        // 首先尝试获取设备信息，而不是直接检查selectedDevices.size
        const devicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();

        // 检查是否有选中的设备
        if (!devicesInfo || devicesInfo.length === 0) {
            showNotification('请先选择要升级的设备', 'warning');
            return;
        }

        console.log('获取到设备信息:', devicesInfo.length, '个设备');

        // 更新选中设备列表显示
        const devicesList = document.getElementById('selectedDevicesList_ota');
        if (devicesList) {
            devicesList.innerHTML = '';

            devicesInfo.forEach(device => {
                const deviceItem = document.createElement('div');
                deviceItem.className = 'list-group-item animate__animated animate__fadeIn';
                deviceItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${device.device_id}</strong>
                            <div class="text-muted small">${device.device_remark || '无备注'}</div>
                        </div>
                        <span class="badge bg-primary">已选择</span>
                    </div>
                `;
                devicesList.appendChild(deviceItem);
            });
        }

        // 更新表单中的隐藏字段
        const form = document.getElementById('batchOtaForm');
        if (form) {
            // 移除之前可能存在的隐藏字段
            const existingInputs = form.querySelectorAll('input[name="device_ids[]"]');
            existingInputs.forEach(input => input.remove());

            // 添加新的隐藏字段
            devicesInfo.forEach(device => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'device_ids[]';
                input.value = device.id;
                form.appendChild(input);
            });
        }

        // 安全地管理模态框实例
        const modalElement = document.getElementById('batchOtaModal');
        if (!modalElement) {
            console.error('找不到批量OTA模态框元素');
            showNotification('模态框初始化失败，请刷新页面重试', 'error');
            return;
        }

        // 销毁之前的模态框实例
        if (batchOtaModalInstance) {
            try {
                batchOtaModalInstance.dispose();
                console.log('已销毁之前的模态框实例');
            } catch (error) {
                console.warn('销毁模态框实例时出错:', error);
            }
            batchOtaModalInstance = null;
        }

        // 创建新的模态框实例
        batchOtaModalInstance = new bootstrap.Modal(modalElement, {
            backdrop: 'static', // 防止点击背景关闭
            keyboard: true      // 允许ESC键关闭
        });

        // 显示模态框
        batchOtaModalInstance.show();
        console.log('批量OTA模态框已显示');

    } catch (error) {
        console.error('显示批量OTA模态框失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

// 关闭批量OTA模态框
function closeBatchOtaModal() {
    if (batchOtaModalInstance && typeof batchOtaModalInstance.hide === 'function') {
        try {
            batchOtaModalInstance.hide();
            console.log('批量OTA模态框已关闭');
        } catch (error) {
            console.warn('关闭模态框时出错:', error);
        }
    }
}

// 处理URL参数（来自OTA页面的快速操作）
function handleUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const deviceId = urlParams.get('device_id');
    const action = urlParams.get('action');

    if (deviceId && action) {
        console.log(`处理快速操作: 设备=${deviceId}, 操作=${action}`);

        // 延迟执行，确保设备列表已加载
        setTimeout(() => {
            // 搜索并选择指定设备
            searchAndSelectDevice(deviceId, action);
        }, 1000);
    }
}

// 搜索并选择设备，然后执行指定操作
async function searchAndSelectDevice(deviceId, action) {
    try {
        // 设置搜索条件
        document.getElementById('searchInput').value = deviceId;

        // 触发搜索
        await loadDeviceList(1);

        // 等待搜索结果加载
        setTimeout(() => {
            // 查找设备并选择
            const deviceCheckboxes = document.querySelectorAll('.device-checkbox');
            let deviceFound = false;

            deviceCheckboxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const deviceIdCell = row.querySelector('td:nth-child(2)');

                if (deviceIdCell && deviceIdCell.textContent.trim() === deviceId) {
                    // 选择设备
                    checkbox.checked = true;
                    DeviceSelectionManager.addDevice(checkbox.value);
                    deviceFound = true;

                    // 高亮显示设备行
                    row.style.backgroundColor = '#fff3cd';
                    setTimeout(() => {
                        row.style.backgroundColor = '';
                    }, 3000);
                }
            });

            if (deviceFound) {
                // 根据操作类型打开相应的模态框
                setTimeout(() => {
                    if (action === 'config_server') {
                        showBatchServerConfigModal();
                        showNotification(`已为设备 ${deviceId} 打开服务器配置`, 'success');
                    } else if (action === 'set_parameters') {
                        showBatchSetParametersModal();
                        showNotification(`已为设备 ${deviceId} 打开参数设置`, 'success');
                    }
                }, 500);
            } else {
                showNotification(`未找到设备 ${deviceId}，请检查设备ID是否正确`, 'warning');
            }
        }, 500);

    } catch (error) {
        console.error('搜索设备失败:', error);
        showNotification('搜索设备失败，请手动查找设备', 'error');
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 处理筛选条件变化
function handleFilterChange() {
    currentFilters = {
        search: document.getElementById('searchInput').value.trim(),
        status: document.getElementById('statusFilter').value,
        product_key: document.getElementById('productKeyFilter').value.trim(),
        firmware: document.getElementById('firmwareFilter').value.trim(),
        ota_status: document.getElementById('otaStatusFilter').value
    };

    currentPage = 1; // 重置到第一页
    loadDeviceList(currentPage);
}

// 切换高级筛选显示/隐藏
function toggleAdvancedFilters() {
    const advancedFilters = document.getElementById('advancedFilters');
    const isVisible = advancedFilters.classList.contains('show');

    if (isVisible) {
        advancedFilters.classList.remove('show');
    } else {
        advancedFilters.classList.add('show');
    }
}

// 应用高级筛选
function applyAdvancedFilters() {
    // 获取所有筛选条件
    currentFilters = {
        search: document.getElementById('searchInput').value.trim(),
        status: document.getElementById('statusFilter').value,
        product_key: document.getElementById('productKeyFilter').value.trim(),
        firmware: document.getElementById('firmwareFilter').value.trim(),
        ota_status: document.getElementById('otaStatusFilter').value,
        // 高级筛选条件
        remark: document.getElementById('remarkFilter').value.trim(),
        debug_status: document.getElementById('debugStatusFilter').value,
        quick_filter: document.getElementById('quickFilter').value,
        create_date_start: document.getElementById('createDateStart').value,
        create_date_end: document.getElementById('createDateEnd').value,
        online_date_start: document.getElementById('onlineDateStart').value,
        online_date_end: document.getElementById('onlineDateEnd').value
    };

    currentPage = 1; // 重置到第一页
    loadDeviceList(currentPage);

    // 显示应用成功的提示
    showNotification('筛选条件已应用', 'success');
}

// 清除所有筛选条件
function clearAllFilters() {
    // 清除基础筛选
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = 'all';
    document.getElementById('productKeyFilter').value = '';
    document.getElementById('firmwareFilter').value = '';
    document.getElementById('otaStatusFilter').value = 'all';

    // 清除高级筛选
    document.getElementById('remarkFilter').value = '';
    document.getElementById('debugStatusFilter').value = 'all';
    document.getElementById('quickFilter').value = 'all';
    document.getElementById('createDateStart').value = '';
    document.getElementById('createDateEnd').value = '';
    document.getElementById('onlineDateStart').value = '';
    document.getElementById('onlineDateEnd').value = '';

    // 重置筛选条件并重新加载
    currentFilters = {};
    currentPage = 1;
    loadDeviceList(currentPage);

    // 显示清除成功的提示
    showNotification('筛选条件已清除', 'info');
}

// 加载设备统计信息
function loadDeviceStats() {
    fetch('/api/devices/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('totalDevices').textContent = data.stats.total;
                document.getElementById('onlineDevices').textContent = data.stats.online;
                document.getElementById('offlineDevices').textContent = data.stats.offline;
            }
        })
        .catch(error => {
            console.error('加载设备统计失败:', error);
        });
}

// 加载设备列表
function loadDeviceList(page = 1) {
    showLoading(true);

    // 构建查询参数
    const params = new URLSearchParams({
        page: page,
        per_page: 20,
        ...currentFilters
    });

    fetch(`/api/devices?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderDeviceList(data.devices);
                renderPagination(data.pagination);
                currentPage = page;
            } else {
                showNotification('加载设备列表失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('加载设备列表失败:', error);
            showNotification('加载设备列表失败，请重试', 'error');
        })
        .finally(() => {
            showLoading(false);
        });
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const tableBody = document.getElementById('deviceTableBody');

    if (show) {
        loadingIndicator.style.display = 'block';
        tableBody.style.opacity = '0.5';
    } else {
        loadingIndicator.style.display = 'none';
        tableBody.style.opacity = '1';
    }
}

// 渲染设备列表 - 优化版本
function renderDeviceList(devices) {
    const tbody = document.getElementById('deviceTableBody');

    // 使用DocumentFragment提高性能
    const fragment = document.createDocumentFragment();

    // 批量创建行
    devices.forEach(device => {
        const row = createDeviceRowOptimized(device);
        fragment.appendChild(row);
    });

    // 一次性替换所有内容
    tbody.innerHTML = '';
    tbody.appendChild(fragment);

    // 更新设备选择状态
    DeviceSelectionManager.updateCurrentPageCheckboxes();

    // 更新全选复选框状态
    DeviceSelectionManager.updateSelectAllCheckbox();
}

// 创建设备行 - 优化版本
function createDeviceRowOptimized(device) {
    const row = document.createElement('tr');

    // 预计算状态徽章 - 使用对象映射提高性能
    const statusBadgeMap = {
        true: '<span class="status-badge status-online"><i class="fas fa-circle me-1"></i>在线</span>',
        false: '<span class="status-badge status-offline"><i class="fas fa-circle me-1"></i>离线</span>'
    };

    const otaStatusBadgeMap = {
        '成功': '<span class="badge bg-success status-badge">成功</span>',
        '失败': '<span class="badge bg-danger status-badge">失败</span>',
        'default': '<span class="badge bg-secondary status-badge">未升级</span>'
    };

    // 使用映射获取徽章
    const statusBadge = statusBadgeMap[device.is_online];
    const firmwareBadge = device.firmware_version && device.firmware_version !== '未知'
        ? `<span class="badge bg-info status-badge">${device.firmware_version}</span>`
        : '<span class="badge bg-secondary status-badge">未知</span>';
    const otaStatusBadge = otaStatusBadgeMap[device.last_ota_status] || otaStatusBadgeMap.default;
    const debugBadge = device.debug_script_enabled
        ? '<span class="badge bg-success status-badge">运行中</span>'
        : '<span class="badge bg-secondary status-badge">已停止</span>';

    // 预处理数据
    const deviceRemark = device.device_remark || '';
    const remarkDisplay = deviceRemark || '-';
    const remarkTitle = deviceRemark || '无备注';
    const isSelected = selectedDevices.has(device.id.toString());

    // 使用模板字符串一次性构建HTML
    row.innerHTML = `
        <td>
            <div class="form-check">
                <input class="form-check-input device-checkbox" type="checkbox" value="${device.id}"
                       ${isSelected ? 'checked' : ''}
                       onchange="handleDeviceSelect(this)">
            </div>
        </td>
        <td class="device-id-cell">
            <div class="d-flex align-items-center">
                <i class="fas fa-microchip text-primary me-2"></i>
                <span>${device.device_id}</span>
            </div>
        </td>
        <td class="hide-sm device-remark-cell">
            <span title="${remarkTitle}">${remarkDisplay}</span>
        </td>
        <td>${statusBadge}</td>
        <td class="hide-md">
            <span class="product-key-cell">${device.product_key}</span>
        </td>
        <td class="hide-lg">
            ${device.device_type_name ? `<span class="badge bg-primary-subtle text-primary"><i class="fas fa-microchip me-1"></i>${device.device_type_name}</span>` : '<span class="badge bg-secondary">未设置</span>'}
        </td>
        <td class="hide-lg">${firmwareBadge}</td>
        <td class="hide-xl">${otaStatusBadge}</td>
        <td class="hide-lg time-cell">
            <i class="far fa-calendar-alt"></i>${device.last_ota_time}
        </td>
        <td class="hide-md time-cell">
            <i class="far fa-clock"></i>${device.last_online_time}
        </td>
        <td class="hide-xl">${debugBadge}</td>
        <td class="action-column">
            <div class="action-buttons-container">
                <button class="action-btn action-btn-upgrade" onclick="startOta('${device.id}')" title="升级固件">
                    <i class="fas fa-sync-alt"></i>升级
                </button>
                <a class="action-btn action-btn-edit" href="/device/edit/${device.id}" target="_blank" title="编辑设备">
                    <i class="fas fa-edit"></i>编辑
                </a>
                <button class="action-btn action-btn-config" onclick="configDeviceServer(${device.id}, '${device.device_id}', '${device.product_key}')" title="配置服务器">
                    <i class="fas fa-server"></i>配置
                </button>
                <a class="action-btn action-btn-ai" href="/ai/device_analysis/${device.id}" title="AI分析">
                    <i class="fas fa-brain"></i>AI分析
                </a>
                <a class="action-btn action-btn-params" href="/device/${device.id}/parameters" title="参数管理">
                    <i class="fas fa-cogs"></i>参数
                </a>
                <a class="action-btn action-btn-console" href="/device/${device.id}/console" title="设备控制台">
                    <i class="fas fa-desktop"></i>控制台
                </a>
                <button class="action-btn action-btn-delete" onclick="deleteDevice(${device.id})" title="删除设备">
                    <i class="fas fa-trash"></i>删除
                </button>
            </div>
        </td>
    `;

    return row;
}

// 保留原函数以兼容性
function createDeviceRow(device) {
    return createDeviceRowOptimized(device);
}

// 渲染分页控件
function renderPagination(pagination) {
    const paginationInfo = document.getElementById('paginationInfo');
    const paginationControls = document.getElementById('paginationControls');
    const totalPagesSpan = document.getElementById('totalPagesSpan');
    const pageJumpInput = document.getElementById('pageJumpInput');

    // 更新分页信息
    const start = (pagination.page - 1) * pagination.per_page + 1;
    const end = Math.min(pagination.page * pagination.per_page, pagination.total);
    paginationInfo.textContent = `显示第 ${start}-${end} 条，共 ${pagination.total} 条记录`;

    // 更新总页数显示
    if (totalPagesSpan) {
        totalPagesSpan.textContent = pagination.pages;
    }

    // 设置页码输入框的最大值
    if (pageJumpInput) {
        pageJumpInput.max = pagination.pages;
        pageJumpInput.placeholder = `1-${pagination.pages}`;
    }

    // 生成分页按钮
    paginationControls.innerHTML = '';

    // 首页按钮
    const firstLi = document.createElement('li');
    firstLi.className = `page-item ${pagination.page === 1 ? 'disabled' : ''}`;
    firstLi.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(1)" title="首页">
        <i class="fas fa-angle-double-left"></i>
    </a>`;
    paginationControls.appendChild(firstLi);

    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${!pagination.has_prev ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(${pagination.prev_num || 1})" title="上一页">
        <i class="fas fa-angle-left"></i>
    </a>`;
    paginationControls.appendChild(prevLi);

    // 页码按钮
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);

    // 如果起始页不是1，显示省略号
    if (startPage > 1) {
        const li = document.createElement('li');
        li.className = 'page-item';
        li.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(1)">1</a>`;
        paginationControls.appendChild(li);

        if (startPage > 2) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
            paginationControls.appendChild(ellipsisLi);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.page ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(${i})">${i}</a>`;
        paginationControls.appendChild(li);
    }

    // 如果结束页不是最后一页，显示省略号
    if (endPage < pagination.pages) {
        if (endPage < pagination.pages - 1) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
            paginationControls.appendChild(ellipsisLi);
        }

        const li = document.createElement('li');
        li.className = 'page-item';
        li.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(${pagination.pages})">${pagination.pages}</a>`;
        paginationControls.appendChild(li);
    }

    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${!pagination.has_next ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(${pagination.next_num || pagination.pages})" title="下一页">
        <i class="fas fa-angle-right"></i>
    </a>`;
    paginationControls.appendChild(nextLi);

    // 末页按钮
    const lastLi = document.createElement('li');
    lastLi.className = `page-item ${pagination.page === pagination.pages ? 'disabled' : ''}`;
    lastLi.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(${pagination.pages})" title="末页">
        <i class="fas fa-angle-double-right"></i>
    </a>`;
    paginationControls.appendChild(lastLi);
}

// 页码跳转功能
function jumpToPage() {
    const pageJumpInput = document.getElementById('pageJumpInput');
    const totalPagesSpan = document.getElementById('totalPagesSpan');

    if (!pageJumpInput || !totalPagesSpan) return;

    const targetPage = parseInt(pageJumpInput.value);
    const totalPages = parseInt(totalPagesSpan.textContent);

    if (isNaN(targetPage) || targetPage < 1 || targetPage > totalPages) {
        showNotification(`请输入有效的页码 (1-${totalPages})`, 'warning');
        pageJumpInput.focus();
        return;
    }

    // 清空输入框
    pageJumpInput.value = '';

    // 跳转到指定页面
    loadDeviceList(targetPage);
}

// 支持回车键跳转
document.addEventListener('DOMContentLoaded', function() {
    const pageJumpInput = document.getElementById('pageJumpInput');
    if (pageJumpInput) {
        pageJumpInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                jumpToPage();
            }
        });
    }
});

// 处理设备选择
function handleDeviceSelect(checkbox) {
    const deviceId = checkbox.value;
    if (checkbox.checked) {
        DeviceSelectionManager.addDevice(deviceId);
    } else {
        DeviceSelectionManager.removeDevice(deviceId);
    }
}

// 处理全选
function handleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllDevices');
    const deviceCheckboxes = document.querySelectorAll('.device-checkbox');

    // 批量更新设备选择状态，避免频繁调用saveSelection
    deviceCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        const deviceId = checkbox.value;
        if (selectAllCheckbox.checked) {
            selectedDevices.add(deviceId);
        } else {
            selectedDevices.delete(deviceId);
        }
    });

    // 一次性保存和更新显示
    DeviceSelectionManager.saveSelection();
    DeviceSelectionManager.updateSelectionDisplay();
}

// 选中全部设备
function selectAllDevices() {
    // 显示加载提示
    showNotification('正在获取所有设备...', 'info');

    // 获取所有设备ID（不分页）
    const params = new URLSearchParams({
        per_page: 999999, // 获取所有设备
        ...currentFilters
    });

    fetch(`/api/devices?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 清空当前选择
                selectedDevices.clear();

                // 添加所有设备到选择集合
                data.devices.forEach(device => {
                    selectedDevices.add(device.id.toString());
                });

                // 更新当前页面的复选框状态
                const deviceCheckboxes = document.querySelectorAll('.device-checkbox');
                deviceCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectedDevices.has(checkbox.value);
                });

                // 更新全选复选框状态
                const selectAllCheckbox = document.getElementById('selectAllDevices');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = true;
                }

                // 保存和更新显示
                DeviceSelectionManager.saveSelection();
                DeviceSelectionManager.updateSelectionDisplay();

                showNotification(`已选中所有 ${data.devices.length} 个设备`, 'success');
            } else {
                showNotification('获取设备列表失败', 'error');
            }
        })
        .catch(error => {
            console.error('获取设备列表失败:', error);
            showNotification('获取设备列表失败', 'error');
        });
}

// 取消选中全部设备
function unselectAllDevices() {
    const deviceCheckboxes = document.querySelectorAll('.device-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllDevices');

    // 获取当前选中的设备数量
    const currentSelectedCount = selectedDevices.size;

    // 清空所有选择
    selectedDevices.clear();

    // 取消选中当前页面所有设备复选框
    deviceCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    // 更新全选复选框状态
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
    }

    // 保存和更新显示
    DeviceSelectionManager.saveSelection();
    DeviceSelectionManager.updateSelectionDisplay();

    showNotification(`已取消选中所有 ${currentSelectedCount} 个设备`, 'info');
}

// 选中在线设备
function selectOnlineDevices() {
    // 显示加载提示
    showNotification('正在获取所有在线设备...', 'info');

    // 获取所有在线设备
    const params = new URLSearchParams({
        per_page: 999999, // 获取所有设备
        status: 'online', // 只获取在线设备
        ...currentFilters
    });

    fetch(`/api/devices?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 添加所有在线设备到选择集合
                data.devices.forEach(device => {
                    selectedDevices.add(device.id.toString());
                });

                // 更新当前页面的复选框状态
                const deviceCheckboxes = document.querySelectorAll('.device-checkbox');
                deviceCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectedDevices.has(checkbox.value);
                });

                // 更新全选复选框状态
                DeviceSelectionManager.updateSelectAllCheckbox();

                // 保存和更新显示
                DeviceSelectionManager.saveSelection();
                DeviceSelectionManager.updateSelectionDisplay();

                showNotification(`已选中所有 ${data.devices.length} 个在线设备`, 'success');
            } else {
                showNotification('获取在线设备列表失败', 'error');
            }
        })
        .catch(error => {
            console.error('获取在线设备列表失败:', error);
            showNotification('获取在线设备列表失败', 'error');
        });
}

// 选中离线设备
function selectOfflineDevices() {
    // 显示加载提示
    showNotification('正在获取所有离线设备...', 'info');

    // 获取所有离线设备
    const params = new URLSearchParams({
        per_page: 999999, // 获取所有设备
        status: 'offline', // 只获取离线设备
        ...currentFilters
    });

    fetch(`/api/devices?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 添加所有离线设备到选择集合
                data.devices.forEach(device => {
                    selectedDevices.add(device.id.toString());
                });

                // 更新当前页面的复选框状态
                const deviceCheckboxes = document.querySelectorAll('.device-checkbox');
                deviceCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectedDevices.has(checkbox.value);
                });

                // 更新全选复选框状态
                DeviceSelectionManager.updateSelectAllCheckbox();

                // 保存和更新显示
                DeviceSelectionManager.saveSelection();
                DeviceSelectionManager.updateSelectionDisplay();

                showNotification(`已选中所有 ${data.devices.length} 个离线设备`, 'success');
            } else {
                showNotification('获取离线设备列表失败', 'error');
            }
        })
        .catch(error => {
            console.error('获取离线设备列表失败:', error);
            showNotification('获取离线设备列表失败', 'error');
        });
}

// 更新全选复选框状态（已由DeviceSelectionManager处理）

// 刷新设备列表（供外部调用）
function refreshDeviceList() {
    loadDeviceStats();
    loadDeviceList(currentPage);
}

// 编辑设备
function editDevice(deviceId) {
    // 在新标签页中打开设备编辑页面
    window.open(`/device/edit/${deviceId}`, '_blank');
}

// 删除设备
function deleteDevice(deviceId) {
    if (!confirm('确定要删除此设备吗？')) {
        return;
    }

    fetch(`/api/device/delete/${deviceId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            selectedDevices.delete(deviceId.toString());
            refreshDeviceList();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除设备失败:', error);
        showNotification('删除设备失败，请重试', 'error');
    });
}

// 配置设备服务器
function configDeviceServer(deviceId, deviceIdStr, currentProductKey) {
    // 设置设备信息
    document.getElementById('configDeviceId').value = deviceIdStr;
    document.getElementById('configCurrentProductKey').value = currentProductKey;
    document.getElementById('newDeviceId').value = '';

    // 存储设备ID用于后续操作
    document.getElementById('deviceServerConfigForm').dataset.deviceId = deviceId;

    // 加载服务器配置信息
    loadServerConfigInfo(currentProductKey);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('deviceServerConfigModal'));
    modal.show();
}

// 加载服务器配置信息
function loadServerConfigInfo(currentProductKey) {
    // 显示当前服务器信息
    fetch('/api/server-config/products')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCurrentServerInfo(currentProductKey, data.server_types, data.products);
            }
        })
        .catch(error => {
            console.error('加载服务器配置失败:', error);
        });

    // 加载可用目标产品
    fetch(`/api/server-config/available-targets/${currentProductKey}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateTargetProductOptions(data.available_targets);
            } else {
                showNotification('加载目标产品失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('加载目标产品失败:', error);
            showNotification('加载目标产品失败，请重试', 'error');
        });
}

// 更新当前服务器信息显示
function updateCurrentServerInfo(productKey, serverTypes, products) {
    const currentServerDetails = document.getElementById('currentServerDetails');

    // 检测当前服务器类型
    let currentServerType = 'unknown';
    let currentProduct = null;

    if (productKey === 'hs7eigK8Xvl') {
        currentServerType = 'alicloud';
    } else if (productKey.startsWith('wx')) {
        currentServerType = 'emqx';
    }

    // 查找当前产品信息
    if (products[currentServerType]) {
        currentProduct = products[currentServerType].find(p => p.product_key === productKey);
    }

    const serverInfo = serverTypes[currentServerType] || { name: '未知服务器' };
    const productName = currentProduct ? currentProduct.name : '未知产品';

    currentServerDetails.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <strong>服务器类型：</strong>${serverInfo.name}
            </div>
            <div class="col-md-6">
                <strong>产品名称：</strong>${productName}
            </div>
        </div>
    `;
}

// 更新目标产品选项
function updateTargetProductOptions(availableTargets) {
    const targetProductSelect = document.getElementById('targetProduct');
    targetProductSelect.innerHTML = '<option value="">请选择目标产品...</option>';

    availableTargets.forEach(target => {
        const option = document.createElement('option');
        option.value = target.product_key;
        option.textContent = `${target.name} (${target.product_key})`;
        option.dataset.migrationDescription = getMigrationDescription(target);
        targetProductSelect.appendChild(option);
    });

    // 添加选择变化事件
    targetProductSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            showMigrationDescription(selectedOption.dataset.migrationDescription);
        } else {
            hideMigrationDescription();
        }
    });
}

// 获取迁移描述
function getMigrationDescription(target) {
    const migrationTypes = {
        'alicloud_to_emqx': '从阿里云IoT平台迁移到EMQX自建服务器',
        'emqx_to_alicloud': '从EMQX自建服务器迁移到阿里云IoT平台',
        'emqx_product_change': '在EMQX服务器内更换产品密钥'
    };

    return migrationTypes[target.migration_type] || '服务器配置变更';
}

// 显示迁移说明
function showMigrationDescription(description) {
    const migrationDiv = document.getElementById('migrationDescription');
    const migrationDetails = document.getElementById('migrationDetails');

    migrationDetails.textContent = description;
    migrationDiv.style.display = 'block';
}

// 隐藏迁移说明
function hideMigrationDescription() {
    document.getElementById('migrationDescription').style.display = 'none';
}

// 确认设备服务器配置
document.getElementById('confirmDeviceServerConfig').addEventListener('click', function() {
    const form = document.getElementById('deviceServerConfigForm');
    const deviceId = form.dataset.deviceId;
    const targetProductKey = document.getElementById('targetProduct').value;
    const newDeviceId = document.getElementById('newDeviceId').value.trim();
    const newDeviceSecret = document.getElementById('newDeviceSecret').value.trim();

    if (!targetProductKey) {
        showNotification('请选择目标产品', 'warning');
        return;
    }

    if (!confirm('确定要更新设备的服务器配置吗？此操作将重启设备。')) {
        return;
    }

    // 发送配置请求
    fetch(`/api/device/${deviceId}/server-config`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            target_product_key: targetProductKey,
            new_device_id: newDeviceId || null,
            new_device_secret: newDeviceSecret || null
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('deviceServerConfigModal'));
            modal.hide();

            // 显示成功消息
            showNotification(data.message, 'success');

            // 刷新设备列表
            refreshDeviceList();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('配置设备服务器失败:', error);
        showNotification('配置设备服务器失败，请重试', 'error');
    });
});

// 批量配置服务器相关函数
async function showBatchServerConfigModal() {
    if (selectedDevices.size === 0) {
        showNotification('请先选择要配置的设备', 'warning');
        return;
    }

    try {
        // 获取选中设备的详细信息
        const devicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();

        // 更新选中设备数量
        document.getElementById('batchSelectedCount').textContent = selectedDevices.size;

        // 加载产品配置
        loadBatchServerConfigProducts();

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('batchServerConfigModal'));
        modal.show();

    } catch (error) {
        console.error('获取设备信息失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

// 加载批量配置的产品列表
function loadBatchServerConfigProducts() {
    fetch('/api/server-config/products')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateBatchProductSelects(data.products);
            } else {
                showNotification('加载产品配置失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('加载产品配置失败:', error);
            showNotification('加载产品配置失败，请重试', 'error');
        });
}

// 填充批量配置的产品选择框
function populateBatchProductSelects(products) {
    const sourceSelect = document.getElementById('batchSourceProductKey');
    const targetSelect = document.getElementById('batchTargetProductKey');

    // 清空选项
    sourceSelect.innerHTML = '<option value="">请选择源产品...</option>';
    targetSelect.innerHTML = '<option value="">请先选择源产品...</option>';

    // 添加所有产品到源产品选择框
    Object.values(products).flat().forEach(product => {
        const option = document.createElement('option');
        option.value = product.product_key;
        option.textContent = `${product.name} (${product.product_key})`;
        option.dataset.serverType = product.server_type;
        sourceSelect.appendChild(option);
    });

    // 添加源产品变化事件
    sourceSelect.addEventListener('change', async function() {
        updateBatchTargetProducts(this.value, products);
        await updateBatchMatchedDevices();
    });

    // 添加目标产品变化事件
    targetSelect.addEventListener('change', function() {
        updateBatchConfigPreview();
        updateConfirmButton();
    });
}

// 更新批量配置的目标产品选项
function updateBatchTargetProducts(sourceProductKey, products) {
    const targetSelect = document.getElementById('batchTargetProductKey');
    targetSelect.innerHTML = '<option value="">请选择目标产品...</option>';

    if (!sourceProductKey) {
        return;
    }

    // 获取可用目标产品
    fetch(`/api/server-config/available-targets/${sourceProductKey}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.available_targets.forEach(target => {
                    const option = document.createElement('option');
                    option.value = target.product_key;
                    option.textContent = `${target.name} (${target.product_key})`;
                    option.dataset.migrationDescription = getMigrationDescription(target);
                    targetSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载目标产品失败:', error);
        });
}

// 更新批量匹配的设备
async function updateBatchMatchedDevices() {
    const sourceProductKey = document.getElementById('batchSourceProductKey').value;
    const matchedDevicesDiv = document.getElementById('batchMatchedDevices');
    const matchedDeviceList = document.getElementById('matchedDeviceList');
    const matchedDeviceCount = document.getElementById('matchedDeviceCount');

    if (!sourceProductKey) {
        matchedDevicesDiv.style.display = 'none';
        return;
    }

    try {
        // 获取所有选中设备的详细信息
        const selectedDevicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();

        // 筛选匹配源产品密钥的设备
        const matchedDevices = selectedDevicesInfo.filter(device =>
            device.product_key === sourceProductKey
        );

        // 显示匹配的设备
        matchedDeviceCount.textContent = matchedDevices.length;
        matchedDeviceList.innerHTML = '';

        if (matchedDevices.length > 0) {
            matchedDevices.forEach(device => {
                const deviceItem = document.createElement('div');
                deviceItem.className = 'border-bottom pb-2 mb-2';
                deviceItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${device.device_id}</strong>
                            <div class="text-muted small">${device.device_remark || '无备注'}</div>
                        </div>
                        <span class="badge bg-success">匹配</span>
                    </div>
                `;
                matchedDeviceList.appendChild(deviceItem);
            });
            matchedDevicesDiv.style.display = 'block';
        } else {
            const noMatchDiv = document.createElement('div');
            noMatchDiv.className = 'text-muted text-center py-3';
            noMatchDiv.textContent = '没有匹配的设备';
            matchedDeviceList.appendChild(noMatchDiv);
            matchedDevicesDiv.style.display = 'block';
        }

        updateConfirmButton();

    } catch (error) {
        console.error('获取设备信息失败:', error);
        matchedDeviceCount.textContent = '0';
        matchedDeviceList.innerHTML = '<div class="text-danger text-center py-3">获取设备信息失败</div>';
        matchedDevicesDiv.style.display = 'block';
        updateConfirmButton();
    }
}

// 更新批量配置预览
function updateBatchConfigPreview() {
    const sourceProductKey = document.getElementById('batchSourceProductKey').value;
    const targetProductKey = document.getElementById('batchTargetProductKey').value;
    const previewDiv = document.getElementById('batchConfigPreview');
    const detailsDiv = document.getElementById('batchConfigDetails');

    if (!sourceProductKey || !targetProductKey) {
        previewDiv.style.display = 'none';
        return;
    }

    // 获取迁移描述
    const targetOption = document.querySelector(`#batchTargetProductKey option[value="${targetProductKey}"]`);
    const migrationDescription = targetOption ? targetOption.dataset.migrationDescription : '服务器配置变更';

    detailsDiv.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <strong>操作类型：</strong>${migrationDescription}
            </div>
            <div class="col-md-6">
                <strong>影响范围：</strong>仅匹配源产品密钥的设备
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                <strong>注意：</strong>配置完成后设备将重启，请确保设备处于可操作状态
            </div>
        </div>
    `;

    previewDiv.style.display = 'block';
}

// 更新确认按钮状态
function updateConfirmButton() {
    const sourceProductKey = document.getElementById('batchSourceProductKey').value;
    const targetProductKey = document.getElementById('batchTargetProductKey').value;
    const matchedCount = parseInt(document.getElementById('matchedDeviceCount').textContent);
    const confirmButton = document.getElementById('confirmBatchServerConfig');

    confirmButton.disabled = !sourceProductKey || !targetProductKey || matchedCount === 0;
}

// 确认批量配置
document.getElementById('confirmBatchServerConfig').addEventListener('click', function() {
    const sourceProductKey = document.getElementById('batchSourceProductKey').value;
    const targetProductKey = document.getElementById('batchTargetProductKey').value;
    const matchedCount = parseInt(document.getElementById('matchedDeviceCount').textContent);

    if (!confirm(`确定要批量配置 ${matchedCount} 个设备的服务器信息吗？此操作将重启这些设备。`)) {
        return;
    }

    // 发送批量配置请求
    fetch('/api/devices/batch-server-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: Array.from(selectedDevices),
            source_product_key: sourceProductKey,
            target_product_key: targetProductKey
        })
    })
    .then(response => response.json())
    .then(data => {
        // 关闭配置模态框
        const configModal = bootstrap.Modal.getInstance(document.getElementById('batchServerConfigModal'));
        configModal.hide();

        // 显示结果模态框
        showBatchConfigResult(data);

        // 刷新设备列表
        refreshDeviceList();
    })
    .catch(error => {
        console.error('批量配置失败:', error);
        showNotification('批量配置失败，请重试', 'error');
    });
});

// 显示批量配置结果
function showBatchConfigResult(data) {
    const resultModal = new bootstrap.Modal(document.getElementById('batchServerConfigResultModal'));
    const summaryDiv = document.getElementById('batchConfigResultSummary');
    const summaryText = document.getElementById('batchConfigResultSummaryText');
    const resultTable = document.getElementById('batchConfigResultTable');

    if (data.success) {
        summaryDiv.className = 'alert alert-success';
        summaryText.innerHTML = `
            <div>${data.message}</div>
            <div class="mt-2">
                <span class="badge bg-success me-2">成功: ${data.summary.success}</span>
                <span class="badge bg-danger">失败: ${data.summary.failed}</span>
            </div>
        `;

        // 显示详细结果
        resultTable.innerHTML = '';
        data.results.forEach(result => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${result.device_id}</td>
                <td>${result.device_name}</td>
                <td>
                    <span class="badge bg-${result.success ? 'success' : 'danger'}">
                        ${result.success ? '成功' : '失败'}
                    </span>
                </td>
                <td>${result.message}</td>
            `;
            resultTable.appendChild(row);
        });
    } else {
        summaryDiv.className = 'alert alert-danger';
        summaryText.textContent = data.message;
        resultTable.innerHTML = '<tr><td colspan="4" class="text-center text-muted">无详细结果</td></tr>';
    }

    resultModal.show();
}

// 下载模板
document.getElementById('downloadTemplate').addEventListener('click', function() {
    window.location.href = "{{ url_for('device.download_import_template') }}";
});

// 处理添加设备表单提交
document.getElementById('addDeviceForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = {
        device_id: document.getElementById('device_id').value.trim(),
        device_remark: document.getElementById('device_remark').value.trim(),
        product_key: document.getElementById('product_key').value.trim(),
        device_type: document.getElementById('device_type').value || null
    };

    if (!formData.device_id || !formData.product_key) {
        showNotification('设备ID和产品密钥不能为空', 'error');
        return;
    }

    fetch('/api/device/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addDeviceModal'));
            modal.hide();

            // 重置表单
            document.getElementById('addDeviceForm').reset();

            // 显示成功消息
            showNotification(data.message, 'success');

            // 刷新设备列表
            refreshDeviceList();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('添加设备失败:', error);
        showNotification('添加设备失败，请重试', 'error');
    });
});

// 处理导入表单提交
document.getElementById('batchImportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);

    fetch("{{ url_for('device.batch_import_devices') }}", {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 更新导入结果
        document.getElementById('successCount').textContent = data.success_count;
        document.getElementById('existingCount').textContent = data.existing_count;
        document.getElementById('failedCount').textContent = data.failed_count;

        // 清空并填充结果表格
        const resultTable = document.getElementById('importResultTable');
        resultTable.innerHTML = '';

        data.results.forEach(result => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${result.device_id}</td>
                <td>
                    <span class="badge ${result.status === 'success' ? 'bg-success' :
                                      result.status === 'existing' ? 'bg-warning' : 'bg-danger'}">
                        ${result.status === 'success' ? '成功' :
                          result.status === 'existing' ? '已存在' : '失败'}
                    </span>
                </td>
                <td>${result.message}</td>
            `;
            resultTable.appendChild(row);
        });

        // 关闭导入模态框，显示结果模态框
        const importModal = bootstrap.Modal.getInstance(document.getElementById('batchImportModal'));
        importModal.hide();
        const resultModal = new bootstrap.Modal(document.getElementById('importResultModal'));
        resultModal.show();
    })
    .catch(error => {
        alert('导入过程中发生错误：' + error);
    });
});

// 批量设置参数相关函数
function showBatchSetParametersModal() {
    // 检查是否有选中的设备
    if (selectedDevices.size === 0) {
        showNotification('请先选择要设置参数的设备', 'warning');
        return;
    }

    // 获取选中设备的详细信息并更新列表
    const devicesList = document.getElementById('selectedDevicesList');
    devicesList.innerHTML = '';

    // 从当前页面获取选中设备的信息
    const currentPageDevices = [];
    const checkboxes = document.querySelectorAll('tbody .device-checkbox:checked');

    checkboxes.forEach(function(checkbox) {
        const deviceId = checkbox.value;
        const deviceRow = checkbox.closest('tr');
        const deviceIdCell = deviceRow.querySelector('td:nth-child(2)');
        const deviceRemarkCell = deviceRow.querySelector('td:nth-child(3)');

        currentPageDevices.push({
            id: deviceId,
            deviceId: deviceIdCell.textContent.trim(),
            remark: deviceRemarkCell.textContent.trim()
        });
    });

    // 显示所有选中的设备（包括其他页面的）
    selectedDevices.forEach(deviceId => {
        // 查找当前页面是否有这个设备的详细信息
        let deviceInfo = currentPageDevices.find(d => d.id === deviceId);

        if (!deviceInfo) {
            // 如果当前页面没有，使用设备ID作为显示信息
            deviceInfo = {
                id: deviceId,
                deviceId: `设备 ${deviceId}`,
                remark: '(其他页面)'
            };
        }

        // 添加设备到列表
        const deviceItem = document.createElement('div');
        deviceItem.className = 'list-group-item';
        deviceItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${deviceInfo.deviceId}</strong>
                    <div class="text-muted small">${deviceInfo.remark}</div>
                </div>
                <span class="badge bg-primary">已选择</span>
            </div>
        `;
        devicesList.appendChild(deviceItem);
    });

    // 清空设置记录
    document.getElementById('batchSetRecords').innerHTML = '';
    
    // 重置表单
    document.getElementById('batchSetParametersForm').reset();
    document.getElementById('paramDescription').textContent = '';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchSetParametersModal'));
    modal.show();
}

// 参数选择变化时更新描述
document.getElementById('batchParamName').addEventListener('change', function() {
    const paramDescription = document.getElementById('paramDescription');
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        paramDescription.textContent = selectedOption.text.split(' - ')[1];
    } else {
        paramDescription.textContent = '';
    }
});

// 批量设置参数
function batchSetParameters() {
    if (selectedDevices.size === 0) {
        showNotification('请选择至少一个设备', 'warning');
        return;
    }

    const paramName = document.getElementById('batchParamName').value;
    const paramValue = document.getElementById('batchParamValue').value;
    const paramAddr = getParamAddress(paramName);

    if (!paramAddr) {
        alert('无效的参数名称');
        return;
    }

    // 清空之前的记录
    document.getElementById('batchSetRecords').innerHTML = '';
    document.getElementById('batchSetSummary').style.display = 'none';

    // 为每个设备创建记录行
    const selectedDeviceIds = Array.from(selectedDevices);
    selectedDeviceIds.forEach(deviceId => {
        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>设备 ${deviceId}</td>
            <td>-</td>
            <td>${paramName}</td>
            <td>${paramValue}</td>
            <td><span class="badge bg-secondary">处理中...</span></td>
            <td>-</td>
        `;
        document.getElementById('batchSetRecords').appendChild(recordRow);
    });

    // 发送批量设置请求
    fetch('/api/devices/batch_parameters', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDeviceIds,
            reg_addr: paramAddr,
            reg_value: parseInt(paramValue)
        }),
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.results) {
            // 更新每个设备的设置结果
            data.results.forEach((result, index) => {
                const recordRow = document.getElementById('batchSetRecords').children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success === true) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '设置成功';
                    errorCell.textContent = '-';
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '设置失败';
                    // 改进错误信息显示，明确区分超时和其他错误
                    let errorMessage = result.error || '未知错误';
                    if (errorMessage.includes('超时') || errorMessage.includes('未收到响应') || errorMessage.includes('timeout')) {
                        errorMessage = '设备超时未响应';
                        statusBadge.textContent = '超时失败';
                    }
                    errorCell.textContent = errorMessage;
                }
            });

            // 显示总体设置结果
            const successCount = data.results.filter(r => r.success === true).length;
            const failCount = data.results.length - successCount;
            const timeoutCount = data.results.filter(r => {
                const error = r.error || '';
                return !r.success && (error.includes('超时') || error.includes('未收到响应') || error.includes('timeout'));
            }).length;

            const summaryDiv = document.getElementById('batchSetSummary');
            const summaryText = document.getElementById('batchSetSummaryText');

            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部设置成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                let summaryMessage = `部分设置成功 (${successCount}/${data.results.length})，${failCount}个设备设置失败`;
                if (timeoutCount > 0) {
                    summaryMessage += `，其中${timeoutCount}个设备超时未响应`;
                }
                summaryText.textContent = summaryMessage;
            }
            summaryDiv.style.display = 'block';
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchSetSummary');
            const summaryText = document.getElementById('batchSetSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `批量设置失败: ${data.error || '服务器响应异常'}`;
            summaryDiv.style.display = 'block';

            // 将所有设备标记为失败
            const recordRows = document.getElementById('batchSetRecords').children;
            for (let i = 0; i < recordRows.length; i++) {
                const statusBadge = recordRows[i].querySelector('.badge');
                const errorCell = recordRows[i].querySelector('td:last-child');
                statusBadge.className = 'badge bg-danger';
                statusBadge.textContent = '请求失败';
                errorCell.textContent = data.error || '服务器响应异常';
            }
        }
    })
    .catch(error => {
        console.error('批量设置参数失败:', error);
        const summaryDiv = document.getElementById('batchSetSummary');
        const summaryText = document.getElementById('batchSetSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = `网络请求失败: ${error.message}`;
        summaryDiv.style.display = 'block';

        // 将所有设备标记为网络错误
        const recordRows = document.getElementById('batchSetRecords').children;
        for (let i = 0; i < recordRows.length; i++) {
            const statusBadge = recordRows[i].querySelector('.badge');
            const errorCell = recordRows[i].querySelector('td:last-child');
            statusBadge.className = 'badge bg-danger';
            statusBadge.textContent = '网络错误';
            errorCell.textContent = error.message;
        }
    });
}

// 获取参数地址
function getParamAddress(paramName) {
    const paramAddresses = {
        'REG_T1': 0, 'REG_T2': 1, 'REG_T3': 2, 'REG_T4': 3, 'REG_T5': 4,
        'REG_T6': 5, 'REG_T7': 6, 'REG_T8': 7, 'REG_T9': 8, 'REG_T10': 9,
        'REG_P1': 10, 'REG_P2': 11, 'REG_P3': 12, 'REG_P4': 13, 'REG_P5': 14,
        'REG_P6': 15, 'REG_P7': 16, 'REG_P8': 17, 'REG_T11': 18, 'REG_CTRL1': 19, 'REG_TEMP1': 20,
        'REG_PERSENTAGE': 24,
        'REG_LOCATION_CODE': 26,
        'REG_LOCATION_LATITUDE_H': 27,
        'REG_LOCATION_LATITUDE_L': 28,
        'REG_LOCATION_LONGITUDE_H': 29,
        'REG_LOCATION_LONGITUDE_L': 30,
        'REG_ERROR_CNT1': 31,
        'REG_ERROR_CNT2': 32,
        'REG_UID_PROTECT_KEY1': 33,
        'REG_HEART_AND_BILLING_PROTO_TYPE': 34,
        'REG_RESERV3': 35,
        'REG_RESERV4': 36,
        'REG_FACTORY_FAULT': 37,
        'REG_FACTORY_FAULT2': 38,
        'REG_P2_PLUG0': 39,
        'REG_P3_PLUG0': 40,
        'REG_P5_PLUG0': 41,
        'REG_P2_PLUG1': 42,
        'REG_P3_PLUG1': 43,
        'REG_P5_PLUG1': 44,
        'REG_P2_PLUG2': 45,
        'REG_P3_PLUG2': 46,
        'REG_P5_PLUG2': 47,
        'REG_P2_PLUG3': 48,
        'REG_P3_PLUG3': 49,
        'REG_P5_PLUG3': 50,
        'REG_P2_PLUG4': 51,
        'REG_P3_PLUG4': 52,
        'REG_P5_PLUG4': 53,
        'REG_P2_PLUG5': 54,
        'REG_P3_PLUG5': 55,
        'REG_P5_PLUG5': 56,
        'REG_P2_PLUG6': 57,
        'REG_P3_PLUG6': 58,
        'REG_P5_PLUG6': 59,
        'REG_P2_PLUG7': 60,
        'REG_P3_PLUG7': 61,
        'REG_P5_PLUG7': 62,
        'REG_P2_PLUG8': 63,
        'REG_P3_PLUG8': 64,
        'REG_P5_PLUG8': 65,
        'REG_P2_PLUG9': 66,
        'REG_P3_PLUG9': 67,
        'REG_P5_PLUG9': 68,
        'REG_UID_PROTECT_KEY2': 77
    };
    return paramAddresses[paramName] || null;
}

// 重复的全选事件监听器已删除，统一使用DeviceSelectionManager处理

// 批量查询参数相关函数
async function showBatchQueryParametersModal() {
    if (selectedDevices.size === 0) {
        showNotification('请先选择要查询参数的设备', 'warning');
        return;
    }

    try {
        // 获取选中设备的详细信息
        const devicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();

        // 更新选中设备列表
        const devicesList = document.getElementById('querySelectedDevicesList');
        devicesList.innerHTML = '';

        // 显示所有选中的设备
        devicesInfo.forEach(device => {
            const deviceItem = document.createElement('div');
            deviceItem.className = 'list-group-item animate__animated animate__fadeIn';
            deviceItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${device.device_id}</strong>
                        <div class="text-muted small">${device.device_remark || '无备注'}</div>
                    </div>
                    <span class="badge bg-primary">已选择</span>
                </div>
            `;
            devicesList.appendChild(deviceItem);
        });

        // 清空查询记录
        document.getElementById('batchQueryRecords').innerHTML = '';
        document.getElementById('batchQuerySummary').style.display = 'none';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('batchQueryParametersModal'));
        modal.show();

    } catch (error) {
        console.error('获取设备信息失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

function confirmBatchQueryParameters() {
    if (!confirm('确定要查询所选设备的参数吗？')) {
        return;
    }

    const selectedDeviceIds = Array.from(selectedDevices);

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchQueryParametersResultTable');
    resultTable.innerHTML = '';
    selectedDeviceIds.forEach(deviceId => {
        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>设备 ${deviceId}</td>
            <td>-</td>
            <td><span class="badge bg-secondary">查询中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 关闭查询模态框，显示结果模态框
    const queryModal = bootstrap.Modal.getInstance(document.getElementById('batchQueryParametersModal'));
    queryModal.hide();
    const resultModal = new bootstrap.Modal(document.getElementById('batchQueryParametersResultModal'));
    resultModal.show();

    // 发送批量查询请求
    fetch('/api/devices/batch_query_parameters', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDeviceIds
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的查询结果
            data.results.forEach((result, index) => {
                const recordRow = resultTable.children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体查询结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchQueryParametersResultSummary');
            const summaryText = document.getElementById('batchQueryParametersResultSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部查询成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分查询成功 (${successCount}/${data.results.length})，${failCount}个设备查询失败`;
            }
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchQueryParametersResultSummary');
            const summaryText = document.getElementById('batchQueryParametersResultSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `查询失败: ${data.error || '未知错误'}`;
        }
    })
    .catch(error => {
        console.error('批量查询参数失败:', error);
        const summaryDiv = document.getElementById('batchQueryParametersResultSummary');
        const summaryText = document.getElementById('batchQueryParametersResultSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
    });
}

// 批量查询位置相关函数
async function showBatchQueryLocationsModal() {
    if (selectedDevices.size === 0) {
        showNotification('请先选择要查询位置的设备', 'warning');
        return;
    }

    try {
        // 获取选中设备的详细信息
        const devicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();

        // 更新选中设备列表
        const devicesList = document.getElementById('locationSelectedDevicesList');
        devicesList.innerHTML = '';

        // 显示所有选中的设备
        devicesInfo.forEach(device => {
            const deviceItem = document.createElement('div');
            deviceItem.className = 'list-group-item animate__animated animate__fadeIn';
            deviceItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${device.device_id}</strong>
                        <div class="text-muted small">${device.device_remark || '无备注'}</div>
                    </div>
                    <span class="badge bg-primary">已选择</span>
                </div>
            `;
            devicesList.appendChild(deviceItem);
        });

        // 清空查询记录
        document.getElementById('batchLocationRecords').innerHTML = '';
        document.getElementById('batchLocationSummary').style.display = 'none';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('batchQueryLocationsModal'));
        modal.show();

    } catch (error) {
        console.error('获取设备信息失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

function confirmBatchQueryLocations() {
    if (!confirm('确定要查询所选设备的位置吗？')) {
        return;
    }

    const selectedDeviceIds = Array.from(selectedDevices);

    // 获取当前页面的设备信息用于显示
    const currentPageDevices = [];
    const deviceRows = document.querySelectorAll('#deviceTableBody tr');
    deviceRows.forEach(row => {
        const checkbox = row.querySelector('.device-checkbox');
        if (checkbox) {
            const deviceIdCell = row.querySelector('td:nth-child(2)');
            const deviceRemarkCell = row.querySelector('td:nth-child(3)');

            currentPageDevices.push({
                id: checkbox.value,
                deviceId: deviceIdCell ? deviceIdCell.textContent.trim() : '',
                remark: deviceRemarkCell ? deviceRemarkCell.textContent.trim() : ''
            });
        }
    });

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchQueryLocationsResultTable');
    resultTable.innerHTML = '';
    selectedDeviceIds.forEach(deviceId => {
        let deviceInfo = currentPageDevices.find(d => d.id === deviceId);
        if (!deviceInfo) {
            deviceInfo = {
                id: deviceId,
                deviceId: `设备 ${deviceId}`,
                remark: '(其他页面)'
            };
        }

        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>${deviceInfo.deviceId}</td>
            <td>${deviceInfo.remark}</td>
            <td>-</td>
            <td>-</td>
            <td>-</td>
            <td><span class="badge bg-secondary">查询中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 关闭查询模态框，显示结果模态框
    const queryModal = bootstrap.Modal.getInstance(document.getElementById('batchQueryLocationsModal'));
    queryModal.hide();
    const resultModal = new bootstrap.Modal(document.getElementById('batchQueryLocationsResultModal'));
    resultModal.show();

    // 发送批量查询请求
    fetch('/api/devices/batch_query_locations', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDeviceIds
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的查询结果
            data.results.forEach((result, index) => {
                const recordRow = resultTable.children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                    
                    // 更新位置信息
                    const location = result.location;
                    recordRow.cells[2].textContent = location.location_code;
                    recordRow.cells[3].textContent = location.latitude.toFixed(6);
                    recordRow.cells[4].textContent = location.longitude.toFixed(6);
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体查询结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchQueryLocationsResultSummary');
            const summaryText = document.getElementById('batchQueryLocationsResultSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部查询成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分查询成功 (${successCount}/${data.results.length})，${failCount}个设备查询失败`;
            }
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchQueryLocationsResultSummary');
            const summaryText = document.getElementById('batchQueryLocationsResultSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `查询失败: ${data.error || '未知错误'}`;
        }
    })
    .catch(error => {
        console.error('批量查询位置失败:', error);
        const summaryDiv = document.getElementById('batchQueryLocationsResultSummary');
        const summaryText = document.getElementById('batchQueryLocationsResultSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
    });
}

// 显示批量调试脚本模态框
async function showBatchDebugScriptModal() {
    if (selectedDevices.size === 0) {
        showNotification('请先选择要操作的设备', 'warning');
        return;
    }

    try {
        // 获取选中设备的详细信息
        const devicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();

        // 更新选中设备列表
        const devicesList = document.getElementById('debugScriptSelectedDevicesList');
        devicesList.innerHTML = '';

        // 显示所有选中的设备
        devicesInfo.forEach(device => {
            const deviceItem = document.createElement('div');
            deviceItem.className = 'list-group-item animate__animated animate__fadeIn';
            deviceItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${device.device_id}</strong>
                        <div class="text-muted small">${device.device_remark || '无备注'}</div>
                    </div>
                    <span class="badge bg-primary">已选择</span>
                </div>
            `;
            devicesList.appendChild(deviceItem);
        });

        // 清空操作记录
        document.getElementById('batchDebugScriptRecords').innerHTML = '';
        document.getElementById('batchDebugScriptSummary').style.display = 'none';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('batchDebugScriptModal'));
        modal.show();

    } catch (error) {
        console.error('获取设备信息失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

// 启动批量调试脚本
function startBatchDebugScript() {
    const selectedDeviceIds = Array.from(selectedDevices);

    const frequency = parseInt(document.getElementById('debugScriptFrequency').value);
    if (frequency < 5) {
        alert('采样频率不能小于5秒');
        return;
    }

    // 获取当前页面的设备信息用于显示
    const currentPageDevices = [];
    const deviceRows = document.querySelectorAll('#deviceTableBody tr');
    deviceRows.forEach(row => {
        const checkbox = row.querySelector('.device-checkbox');
        if (checkbox) {
            const deviceIdCell = row.querySelector('td:nth-child(2)');
            const deviceRemarkCell = row.querySelector('td:nth-child(3)');

            currentPageDevices.push({
                id: checkbox.value,
                deviceId: deviceIdCell ? deviceIdCell.textContent.trim() : '',
                remark: deviceRemarkCell ? deviceRemarkCell.textContent.trim() : ''
            });
        }
    });

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchDebugScriptRecords');
    resultTable.innerHTML = '';
    selectedDeviceIds.forEach(deviceId => {
        let deviceInfo = currentPageDevices.find(d => d.id === deviceId);
        if (!deviceInfo) {
            deviceInfo = {
                id: deviceId,
                deviceId: `设备 ${deviceId}`,
                remark: '(其他页面)'
            };
        }

        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>${deviceInfo.deviceId}</td>
            <td>${deviceInfo.remark}</td>
            <td>启动脚本</td>
            <td><span class="badge bg-secondary">处理中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 发送批量启动请求
    fetch('/debug_script/api/devices/batch_start_debug_script', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDeviceIds.map(d => parseInt(d)),
            frequency: frequency
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的操作结果
            data.results.forEach((result, index) => {
                const recordRow = resultTable.children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体操作结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchDebugScriptSummary');
            const summaryText = document.getElementById('batchDebugScriptSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部启动成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分启动成功 (${successCount}/${data.results.length})，${failCount}个设备启动失败`;
            }
            summaryDiv.style.display = 'block';
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchDebugScriptSummary');
            const summaryText = document.getElementById('batchDebugScriptSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `启动失败: ${data.error || '未知错误'}`;
            summaryDiv.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('批量启动调试脚本失败:', error);
        const summaryDiv = document.getElementById('batchDebugScriptSummary');
        const summaryText = document.getElementById('batchDebugScriptSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
        summaryDiv.style.display = 'block';
    });
}

// 停止批量调试脚本
function stopBatchDebugScript() {
    const selectedDeviceIds = Array.from(selectedDevices);

    // 获取当前页面的设备信息用于显示
    const currentPageDevices = [];
    const deviceRows = document.querySelectorAll('#deviceTableBody tr');
    deviceRows.forEach(row => {
        const checkbox = row.querySelector('.device-checkbox');
        if (checkbox) {
            const deviceIdCell = row.querySelector('td:nth-child(2)');
            const deviceRemarkCell = row.querySelector('td:nth-child(3)');

            currentPageDevices.push({
                id: checkbox.value,
                deviceId: deviceIdCell ? deviceIdCell.textContent.trim() : '',
                remark: deviceRemarkCell ? deviceRemarkCell.textContent.trim() : ''
            });
        }
    });

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchDebugScriptRecords');
    resultTable.innerHTML = '';
    selectedDeviceIds.forEach(deviceId => {
        let deviceInfo = currentPageDevices.find(d => d.id === deviceId);
        if (!deviceInfo) {
            deviceInfo = {
                id: deviceId,
                deviceId: `设备 ${deviceId}`,
                remark: '(其他页面)'
            };
        }

        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>${deviceInfo.deviceId}</td>
            <td>${deviceInfo.remark}</td>
            <td>停止脚本</td>
            <td><span class="badge bg-secondary">处理中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 发送批量停止请求
    fetch('/debug_script/api/devices/batch_stop_debug_script', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDeviceIds.map(d => parseInt(d))
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的操作结果
            data.results.forEach((result, index) => {
                const recordRow = resultTable.children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体操作结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchDebugScriptSummary');
            const summaryText = document.getElementById('batchDebugScriptSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部停止成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分停止成功 (${successCount}/${data.results.length})，${failCount}个设备停止失败`;
            }
            summaryDiv.style.display = 'block';
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchDebugScriptSummary');
            const summaryText = document.getElementById('batchDebugScriptSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `停止失败: ${data.error || '未知错误'}`;
            summaryDiv.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('批量停止调试脚本失败:', error);
        const summaryDiv = document.getElementById('batchDebugScriptSummary');
        const summaryText = document.getElementById('batchDebugScriptSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
        summaryDiv.style.display = 'block';
    });
}

// 批量修改产品密钥相关函数
async function showBatchUpdateProductKeyModal() {
    if (selectedDevices.size === 0) {
        showNotification('请先选择要修改产品密钥的设备', 'warning');
        return;
    }

    try {
        // 获取选中设备的详细信息
        const devicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();

        // 更新选中设备数量
        document.getElementById('selectedDeviceCount').textContent = selectedDevices.size;

        // 更新选中设备列表
        const devicesList = document.getElementById('batchUpdateSelectedDevicesList');
        devicesList.innerHTML = '';

        // 显示所有选中的设备
        devicesInfo.forEach(device => {
            const deviceItem = document.createElement('div');
            deviceItem.className = 'list-group-item animate__animated animate__fadeIn';
            deviceItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${device.device_id}</strong>
                        <div class="text-muted small">${device.device_remark || '无备注'}</div>
                        <div class="text-muted small">当前产品密钥: ${device.product_key || '未知'}</div>
                    </div>
                    <span class="badge bg-primary">已选择</span>
                </div>
            `;
            devicesList.appendChild(deviceItem);
        });

        // 重置表单和结果区域
        document.getElementById('batchUpdateProductKeyForm').reset();
        document.getElementById('batchUpdateResultSection').style.display = 'none';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('batchUpdateProductKeyModal'));
        modal.show();

    } catch (error) {
        console.error('获取设备信息失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

// 确认批量修改产品密钥
document.getElementById('confirmBatchUpdateProductKey').addEventListener('click', function() {
    const newProductKey = document.getElementById('newProductKey').value.trim();

    if (!newProductKey) {
        showNotification('请输入新的产品密钥', 'warning');
        return;
    }

    if (selectedDevices.size === 0) {
        showNotification('没有选中的设备', 'warning');
        return;
    }

    // 确认操作
    if (!confirm(`确定要将 ${selectedDevices.size} 个设备的产品密钥修改为 "${newProductKey}" 吗？\n\n此操作不可逆，请确认产品密钥正确。`)) {
        return;
    }

    // 禁用按钮，显示加载状态
    const confirmBtn = document.getElementById('confirmBatchUpdateProductKey');
    const originalText = confirmBtn.innerHTML;
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>修改中...';

    // 发送请求
    fetch('/api/devices/batch-update-product-key', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: Array.from(selectedDevices),
            new_product_key: newProductKey
        })
    })
    .then(response => response.json())
    .then(data => {
        // 恢复按钮状态
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = originalText;

        if (data.success) {
            // 显示成功消息
            showNotification(data.message, 'success');

            // 显示详细结果
            displayBatchUpdateResults(data);

            // 刷新设备列表
            refreshDeviceList();
        } else {
            showNotification(data.message, 'error');
            if (data.results) {
                displayBatchUpdateResults(data);
            }
        }
    })
    .catch(error => {
        // 恢复按钮状态
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = originalText;

        console.error('批量修改产品密钥失败:', error);
        showNotification('批量修改产品密钥失败，请重试', 'error');
    });
});

// 显示批量修改结果
function displayBatchUpdateResults(data) {
    const resultSection = document.getElementById('batchUpdateResultSection');
    const summaryDiv = document.getElementById('batchUpdateSummary');
    const summaryText = document.getElementById('batchUpdateSummaryText');
    const recordsTable = document.getElementById('batchUpdateRecords');

    // 显示结果区域
    resultSection.style.display = 'block';

    // 更新摘要信息
    const summary = data.summary;
    if (summary.failed === 0) {
        summaryDiv.className = 'alert alert-success mb-3';
        summaryText.textContent = `全部修改成功！共修改 ${summary.success} 个设备的产品密钥`;
    } else if (summary.success > 0) {
        summaryDiv.className = 'alert alert-warning mb-3';
        summaryText.textContent = `部分修改成功！成功修改 ${summary.success} 个设备，失败 ${summary.failed} 个设备`;
    } else {
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = `修改失败！${summary.failed} 个设备修改失败`;
    }

    // 清空并填充结果表格
    recordsTable.innerHTML = '';
    data.results.forEach(result => {
        const row = document.createElement('tr');

        const statusBadge = document.createElement('span');
        statusBadge.className = result.status === 'success' ? 'badge bg-success' : 'badge bg-danger';
        statusBadge.textContent = result.status === 'success' ? '成功' : '失败';

        row.innerHTML = `
            <td>${result.device_id}</td>
            <td>${result.device_remark}</td>
            <td><code>${result.old_product_key}</code></td>
            <td><code>${result.new_product_key}</code></td>
            <td></td>
            <td>${result.error || '-'}</td>
        `;

        // 插入状态徽章
        row.children[4].appendChild(statusBadge);
        recordsTable.appendChild(row);
    });
}

// 批量重启调试脚本（自动检测运行中的设备）
async function batchRestartDebugScripts() {
    try {
        // 获取当前正在运行调试脚本的设备
        const response = await fetch('/debug_script/api/running_devices');
        const data = await response.json();

        if (!response.ok || !data.success) {
            showNotification('获取运行中设备列表失败: ' + (data.error || '未知错误'), 'error');
            return;
        }

        const runningDevices = data.running_devices || [];

        if (runningDevices.length === 0) {
            showNotification('当前没有正在运行调试脚本的设备', 'info');
            return;
        }

        // 显示确认对话框，包含设备列表
        const deviceList = runningDevices.map(device =>
            `${device.device_id} (${device.device_remark || '无备注'})`
        ).join('\n');

        const confirmMessage = `发现 ${runningDevices.length} 个正在运行调试脚本的设备：\n\n${deviceList}\n\n确定要重启这些设备的调试脚本吗？`;

        if (!confirm(confirmMessage)) {
            return;
        }

        // 执行批量重启操作
        await executeBatchDebugScriptOperationAuto(runningDevices, 'restart', '重启调试脚本');

    } catch (error) {
        console.error('批量重启调试脚本失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

// 批量停止调试脚本（自动检测运行中的设备）
async function batchStopDebugScripts() {
    try {
        // 获取当前正在运行调试脚本的设备
        const response = await fetch('/debug_script/api/running_devices');
        const data = await response.json();

        if (!response.ok || !data.success) {
            showNotification('获取运行中设备列表失败: ' + (data.error || '未知错误'), 'error');
            return;
        }

        const runningDevices = data.running_devices || [];

        if (runningDevices.length === 0) {
            showNotification('当前没有正在运行调试脚本的设备', 'info');
            return;
        }

        // 显示确认对话框，包含设备列表
        const deviceList = runningDevices.map(device =>
            `${device.device_id} (${device.device_remark || '无备注'})`
        ).join('\n');

        const confirmMessage = `发现 ${runningDevices.length} 个正在运行调试脚本的设备：\n\n${deviceList}\n\n确定要停止这些设备的调试脚本吗？`;

        if (!confirm(confirmMessage)) {
            return;
        }

        // 执行批量停止操作
        await executeBatchDebugScriptOperationAuto(runningDevices, 'stop', '停止调试脚本');

    } catch (error) {
        console.error('批量停止调试脚本失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

// 执行批量调试脚本操作（自动检测设备版本）
async function executeBatchDebugScriptOperationAuto(runningDevices, operation, operationName) {
    // 显示结果模态框
    const modal = new bootstrap.Modal(document.getElementById('batchDebugScriptResultModal'));
    document.getElementById('batchDebugScriptResultModalLabel').innerHTML =
        `<i class="fas fa-list-alt me-2"></i>批量${operationName}结果`;

    // 初始化统计
    const totalCount = runningDevices.length;
    let successCount = 0;
    let failedCount = 0;
    let skippedCount = 0;
    let processedCount = 0;

    // 更新统计显示
    document.getElementById('totalDevicesCount').textContent = totalCount;
    document.getElementById('successDevicesCount').textContent = '0';
    document.getElementById('failedDevicesCount').textContent = '0';
    document.getElementById('skippedDevicesCount').textContent = '0';

    // 清空结果表格
    const resultsTable = document.getElementById('batchDebugScriptResults');
    resultsTable.innerHTML = '';

    // 显示进度条
    const progressContainer = document.getElementById('batchDebugScriptProgress');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    progressContainer.style.display = 'block';

    modal.show();

    try {
        for (const deviceInfo of runningDevices) {
            try {
                // 更新进度
                processedCount++;
                const progress = (processedCount / totalCount) * 100;
                progressBar.style.width = `${progress}%`;
                progressText.textContent = `${processedCount}/${totalCount}`;

                // 执行操作
                const result = await executeDebugScriptOperation(deviceInfo.id, operation);

                // 添加结果到表格
                addDebugScriptResultRow(deviceInfo, operation, result);

                if (result.success) {
                    successCount++;
                } else {
                    failedCount++;
                }

            } catch (error) {
                console.error(`处理设备 ${deviceInfo.id} 时出错:`, error);

                // 添加错误结果
                addDebugScriptResultRow(
                    deviceInfo,
                    operation,
                    { success: false, message: error.message || '操作异常' }
                );
                failedCount++;
            }

            // 更新统计显示
            document.getElementById('successDevicesCount').textContent = successCount;
            document.getElementById('failedDevicesCount').textContent = failedCount;
            document.getElementById('skippedDevicesCount').textContent = skippedCount;
        }

        // 隐藏进度条
        progressContainer.style.display = 'none';

        // 显示导出按钮
        document.getElementById('exportResultsBtn').style.display = 'inline-block';

        // 显示完成通知
        if (failedCount === 0) {
            showNotification(`批量${operationName}完成，全部成功！`, 'success');
        } else {
            showNotification(`批量${operationName}完成，成功 ${successCount} 个，失败 ${failedCount} 个`, 'warning');
        }

    } catch (error) {
        console.error('批量操作失败:', error);
        showNotification(`批量${operationName}失败: ${error.message}`, 'error');
        progressContainer.style.display = 'none';
    }
}

// 执行批量调试脚本操作（原版本，保留兼容性）
async function executeBatchDebugScriptOperation(operation, operationName) {
    // 显示结果模态框
    const modal = new bootstrap.Modal(document.getElementById('batchDebugScriptResultModal'));
    document.getElementById('batchDebugScriptResultModalLabel').innerHTML =
        `<i class="fas fa-list-alt me-2"></i>批量${operationName}结果`;

    // 初始化统计
    const totalCount = selectedDevices.size;
    let successCount = 0;
    let failedCount = 0;
    let skippedCount = 0;
    let processedCount = 0;

    // 更新统计显示
    document.getElementById('totalDevicesCount').textContent = totalCount;
    document.getElementById('successDevicesCount').textContent = '0';
    document.getElementById('failedDevicesCount').textContent = '0';
    document.getElementById('skippedDevicesCount').textContent = '0';

    // 清空结果表格
    const resultsTable = document.getElementById('batchDebugScriptResults');
    resultsTable.innerHTML = '';

    // 显示进度条
    const progressContainer = document.getElementById('batchDebugScriptProgress');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    progressContainer.style.display = 'block';

    modal.show();

    try {
        // 获取选中设备的详细信息
        const deviceIds = Array.from(selectedDevices);

        for (const deviceId of deviceIds) {
            try {
                // 更新进度
                processedCount++;
                const progress = (processedCount / totalCount) * 100;
                progressBar.style.width = `${progress}%`;
                progressText.textContent = `${processedCount}/${totalCount}`;

                // 获取设备信息
                const deviceInfo = await getDeviceInfo(deviceId);

                // 执行操作
                const result = await executeDebugScriptOperation(deviceId, operation);

                // 添加结果到表格
                addDebugScriptResultRow(deviceInfo, operation, result);

                if (result.success) {
                    successCount++;
                } else {
                    failedCount++;
                }

            } catch (error) {
                console.error(`处理设备 ${deviceId} 时出错:`, error);

                // 添加错误结果
                addDebugScriptResultRow(
                    { device_id: deviceId, remark: '未知' },
                    operation,
                    { success: false, message: error.message || '操作异常' }
                );
                failedCount++;
            }

            // 更新统计显示
            document.getElementById('successDevicesCount').textContent = successCount;
            document.getElementById('failedDevicesCount').textContent = failedCount;
            document.getElementById('skippedDevicesCount').textContent = skippedCount;
        }

        // 隐藏进度条
        progressContainer.style.display = 'none';

        // 显示导出按钮
        document.getElementById('exportResultsBtn').style.display = 'inline-block';

        // 显示完成通知
        if (failedCount === 0) {
            showNotification(`批量${operationName}完成，全部成功！`, 'success');
        } else {
            showNotification(`批量${operationName}完成，成功 ${successCount} 个，失败 ${failedCount} 个`, 'warning');
        }

    } catch (error) {
        console.error('批量操作失败:', error);
        showNotification(`批量${operationName}失败: ${error.message}`, 'error');
        progressContainer.style.display = 'none';
    }
}

// 获取设备信息
async function getDeviceInfo(deviceId) {
    try {
        const response = await fetch(`/api/device/${deviceId}`);
        const data = await response.json();

        if (data.success) {
            return data.device;
        } else {
            return { device_id: deviceId, remark: '未知' };
        }
    } catch (error) {
        return { device_id: deviceId, remark: '未知' };
    }
}

// 执行单个设备的调试脚本操作
async function executeDebugScriptOperation(deviceId, operation) {
    try {
        const endpoint = operation === 'restart' ? 'start' : 'stop';
        const url = `/debug_script/${endpoint}/${deviceId}`;

        const requestBody = operation === 'restart' ?
            JSON.stringify({ frequency: 60 }) : // 默认频率60秒
            undefined;

        const response = await fetch(url, {
            method: 'POST',
            headers: operation === 'restart' ? {
                'Content-Type': 'application/json'
            } : {},
            body: requestBody
        });

        const data = await response.json();

        if (response.ok && data.success) {
            return {
                success: true,
                message: operation === 'restart' ? '调试脚本启动成功' : '调试脚本停止成功'
            };
        } else {
            return {
                success: false,
                message: data.error || data.message || '操作失败'
            };
        }

    } catch (error) {
        return {
            success: false,
            message: `网络错误: ${error.message}`
        };
    }
}

// 添加调试脚本操作结果行
function addDebugScriptResultRow(deviceInfo, operation, result) {
    const resultsTable = document.getElementById('batchDebugScriptResults');
    const row = document.createElement('tr');

    // 状态徽章
    const statusBadge = document.createElement('span');
    if (result.success) {
        statusBadge.className = 'badge bg-success';
        statusBadge.textContent = '成功';
    } else {
        statusBadge.className = 'badge bg-danger';
        statusBadge.textContent = '失败';
    }

    const operationText = operation === 'restart' ? '重启调试脚本' : '停止调试脚本';

    row.innerHTML = `
        <td>${deviceInfo.device_id || '未知'}</td>
        <td>${deviceInfo.remark || '-'}</td>
        <td>${operationText}</td>
        <td></td>
        <td>${result.message || '-'}</td>
    `;

    // 插入状态徽章
    row.children[3].appendChild(statusBadge);
    resultsTable.appendChild(row);
}

// 批量查询固件信息相关函数
async function showBatchQueryFirmwareInfoModal() {
    if (selectedDevices.size === 0) {
        showNotification('请先选择要查询固件信息的设备', 'warning');
        return;
    }

    try {
        // 获取选中设备的详细信息
        const devicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();

        // 更新选中设备列表
        const devicesList = document.getElementById('firmwareInfoSelectedDevicesList');
        devicesList.innerHTML = '';

        // 显示所有选中的设备
        devicesInfo.forEach(device => {
            const deviceItem = document.createElement('div');
            deviceItem.className = 'list-group-item animate__animated animate__fadeIn';
            deviceItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${device.device_id}</strong>
                        <small class="text-muted d-block">${device.remark || '无备注'}</small>
                    </div>
                    <span class="badge bg-primary">${device.device_type_name || '未设置'}</span>
                </div>
            `;
            devicesList.appendChild(deviceItem);
        });

        // 清空查询记录
        document.getElementById('batchQueryFirmwareInfoRecords').innerHTML = '';
        document.getElementById('batchQueryFirmwareInfoSummary').style.display = 'none';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('batchQueryFirmwareInfoModal'));
        modal.show();

    } catch (error) {
        console.error('获取设备信息失败:', error);
        showNotification('获取设备信息失败，请重试', 'error');
    }
}

async function executeBatchQueryFirmwareInfo() {
    const selectedDeviceIds = Array.from(selectedDevices);

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchQueryFirmwareInfoRecords');
    resultTable.innerHTML = '';
    selectedDeviceIds.forEach(deviceId => {
        let deviceInfo = currentPageDevices.find(d => d.id === deviceId);
        if (!deviceInfo) {
            deviceInfo = {
                id: deviceId,
                device_id: `设备 ${deviceId}`,
                device_remark: '-'
            };
        }

        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>${deviceInfo.device_id}</td>
            <td>${deviceInfo.device_remark || '-'}</td>
            <td id="device_type_${deviceId}">-</td>
            <td id="firmware_version_${deviceId}">-</td>
            <td><span class="badge bg-secondary">查询中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 显示查询结果区域
    document.getElementById('batchQueryFirmwareInfoSummary').style.display = 'block';
    document.getElementById('batchQueryFirmwareInfoSummaryText').textContent = '正在查询固件信息...';

    // 逐个查询设备固件信息
    let successCount = 0;
    let failCount = 0;

    for (const deviceId of selectedDeviceIds) {
        try {
            const response = await fetch(`/api/device/${deviceId}/firmware_info`);
            const data = await response.json();

            const deviceTypeCell = document.getElementById(`device_type_${deviceId}`);
            const firmwareVersionCell = document.getElementById(`firmware_version_${deviceId}`);
            const statusCell = deviceTypeCell.parentElement.children[4];
            const errorCell = deviceTypeCell.parentElement.children[5];

            if (data.result === 0) {
                // 查询成功
                const info = data.info || {};
                deviceTypeCell.innerHTML = info.device_type_name || '未知';
                firmwareVersionCell.innerHTML = `${info.fw1_version || '未知'} / ${info.fw2_version || '未知'}`;
                statusCell.innerHTML = '<span class="badge bg-success">成功</span>';
                errorCell.textContent = '-';
                successCount++;
            } else {
                // 查询失败
                deviceTypeCell.textContent = '-';
                firmwareVersionCell.textContent = '-';
                statusCell.innerHTML = '<span class="badge bg-danger">失败</span>';
                errorCell.textContent = '查询失败';
                failCount++;
            }
        } catch (error) {
            console.error(`查询设备 ${deviceId} 固件信息失败:`, error);
            const deviceTypeCell = document.getElementById(`device_type_${deviceId}`);
            const statusCell = deviceTypeCell.parentElement.children[4];
            const errorCell = deviceTypeCell.parentElement.children[5];

            statusCell.innerHTML = '<span class="badge bg-danger">错误</span>';
            errorCell.textContent = error.message || '网络错误';
            failCount++;
        }
    }

    // 更新汇总信息
    const summaryText = document.getElementById('batchQueryFirmwareInfoSummaryText');
    summaryText.textContent = `查询完成，成功: ${successCount}个，失败: ${failCount}个`;

    if (failCount === 0) {
        document.getElementById('batchQueryFirmwareInfoSummary').className = 'alert alert-success mb-3';
    } else if (successCount === 0) {
        document.getElementById('batchQueryFirmwareInfoSummary').className = 'alert alert-danger mb-3';
    } else {
        document.getElementById('batchQueryFirmwareInfoSummary').className = 'alert alert-warning mb-3';
    }
}
</script>
{% endblock %}